{".class": "MypyFile", "_fullname": "src.data_trans.crawlers.advanced_crawler", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AdvancedCrawler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["src.data_trans.crawlers.base_crawler.BaseCrawler"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "src.data_trans.crawlers.advanced_crawler.AdvancedCrawler", "name": "AdvancedCrawler", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "src.data_trans.crawlers.advanced_crawler.AdvancedCrawler", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "src.data_trans.crawlers.advanced_crawler", "mro": ["src.data_trans.crawlers.advanced_crawler.AdvancedCrawler", "src.data_trans.crawlers.base_crawler.BaseCrawler", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "src.data_trans.crawlers.advanced_crawler.AdvancedCrawler.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "config"], "arg_types": ["src.data_trans.crawlers.advanced_crawler.AdvancedCrawler", "src.data_trans.crawlers.advanced_crawler.AdvancedCrawlerConfig"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of AdvancedCrawler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_anti_detection": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "src.data_trans.crawlers.advanced_crawler.AdvancedCrawler._anti_detection", "name": "_anti_detection", "setter_type": null, "type": {".class": "UnionType", "items": ["src.data_trans.crawlers.anti_detection.AntiDetection", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_cache_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "url", "result"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "src.data_trans.crawlers.advanced_crawler.AdvancedCrawler._cache_response", "name": "_cache_response", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "url", "result"], "arg_types": ["src.data_trans.crawlers.advanced_crawler.AdvancedCrawler", "builtins.str", "src.data_trans.crawlers.base_crawler.CrawlResult"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_cache_response of AdvancedCrawler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_captcha_solver": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "src.data_trans.crawlers.advanced_crawler.AdvancedCrawler._captcha_solver", "name": "_captcha_solver", "setter_type": null, "type": {".class": "UnionType", "items": ["src.data_trans.crawlers.captcha_solver.CaptchaSolver", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_fetch_single_internal": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "url", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "src.data_trans.crawlers.advanced_crawler.AdvancedCrawler._fetch_single_internal", "name": "_fetch_single_internal", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "url", "kwargs"], "arg_types": ["src.data_trans.crawlers.advanced_crawler.AdvancedCrawler", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_fetch_single_internal of AdvancedCrawler", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "src.data_trans.crawlers.base_crawler.CrawlResult"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_fetch_with_http_client": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "url", "params"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "src.data_trans.crawlers.advanced_crawler.AdvancedCrawler._fetch_with_http_client", "name": "_fetch_with_http_client", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "url", "params"], "arg_types": ["src.data_trans.crawlers.advanced_crawler.AdvancedCrawler", "builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_fetch_with_http_client of AdvancedCrawler", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "src.data_trans.crawlers.base_crawler.CrawlResult"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_fetch_with_js_rendering": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "url", "params"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "src.data_trans.crawlers.advanced_crawler.AdvancedCrawler._fetch_with_js_rendering", "name": "_fetch_with_js_rendering", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "url", "params"], "arg_types": ["src.data_trans.crawlers.advanced_crawler.AdvancedCrawler", "builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_fetch_with_js_rendering of AdvancedCrawler", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "src.data_trans.crawlers.base_crawler.CrawlResult"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_fetch_with_smart_retry": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "url", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "src.data_trans.crawlers.advanced_crawler.AdvancedCrawler._fetch_with_smart_retry", "name": "_fetch_with_smart_retry", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "url", "kwargs"], "arg_types": ["src.data_trans.crawlers.advanced_crawler.AdvancedCrawler", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_fetch_with_smart_retry of AdvancedCrawler", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "src.data_trans.crawlers.base_crawler.CrawlResult"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_cached_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "url"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "src.data_trans.crawlers.advanced_crawler.AdvancedCrawler._get_cached_response", "name": "_get_cached_response", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "url"], "arg_types": ["src.data_trans.crawlers.advanced_crawler.AdvancedCrawler", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_cached_response of AdvancedCrawler", "ret_type": {".class": "UnionType", "items": ["src.data_trans.crawlers.base_crawler.CrawlResult", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_handle_captcha": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "url", "result", "params"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "src.data_trans.crawlers.advanced_crawler.AdvancedCrawler._handle_captcha", "name": "_handle_captcha", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "url", "result", "params"], "arg_types": ["src.data_trans.crawlers.advanced_crawler.AdvancedCrawler", "builtins.str", "src.data_trans.crawlers.base_crawler.CrawlResult", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_handle_captcha of AdvancedCrawler", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "src.data_trans.crawlers.base_crawler.CrawlResult"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_js_renderer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "src.data_trans.crawlers.advanced_crawler.AdvancedCrawler._js_renderer", "name": "_js_renderer", "setter_type": null, "type": {".class": "UnionType", "items": ["src.data_trans.crawlers.js_renderer.JSRenderer", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_make_http_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "client", "url", "params"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "src.data_trans.crawlers.advanced_crawler.AdvancedCrawler._make_http_request", "name": "_make_http_request", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "client", "url", "params"], "arg_types": ["src.data_trans.crawlers.advanced_crawler.AdvancedCrawler", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_make_http_request of AdvancedCrawler", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_needs_captcha_solving": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "result"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "src.data_trans.crawlers.advanced_crawler.AdvancedCrawler._needs_captcha_solving", "name": "_needs_captcha_solving", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "result"], "arg_types": ["src.data_trans.crawlers.advanced_crawler.AdvancedCrawler", "src.data_trans.crawlers.base_crawler.CrawlResult"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_needs_captcha_solving of AdvancedCrawler", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_parse_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "response", "url"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "src.data_trans.crawlers.advanced_crawler.AdvancedCrawler._parse_response", "name": "_parse_response", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "response", "url"], "arg_types": ["src.data_trans.crawlers.advanced_crawler.AdvancedCrawler", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_parse_response of AdvancedCrawler", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_prepare_request_params": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "url", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "src.data_trans.crawlers.advanced_crawler.AdvancedCrawler._prepare_request_params", "name": "_prepare_request_params", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "url", "kwargs"], "arg_types": ["src.data_trans.crawlers.advanced_crawler.AdvancedCrawler", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_prepare_request_params of AdvancedCrawler", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_proxy_pool": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "src.data_trans.crawlers.advanced_crawler.AdvancedCrawler._proxy_pool", "name": "_proxy_pool", "setter_type": null, "type": {".class": "UnionType", "items": ["src.data_trans.crawlers.proxy_pool.ProxyPool", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_report_request_result": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "session_id", "success"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "src.data_trans.crawlers.advanced_crawler.AdvancedCrawler._report_request_result", "name": "_report_request_result", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "session_id", "success"], "arg_types": ["src.data_trans.crawlers.advanced_crawler.AdvancedCrawler", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_report_request_result of AdvancedCrawler", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_response_cache": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "src.data_trans.crawlers.advanced_crawler.AdvancedCrawler._response_cache", "name": "_response_cache", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_session_manager": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "src.data_trans.crawlers.advanced_crawler.AdvancedCrawler._session_manager", "name": "_session_manager", "setter_type": null, "type": {".class": "UnionType", "items": ["src.data_trans.crawlers.session_manager.SessionManager", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_should_retry_result": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "result"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "src.data_trans.crawlers.advanced_crawler.AdvancedCrawler._should_retry_result", "name": "_should_retry_result", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "result"], "arg_types": ["src.data_trans.crawlers.advanced_crawler.AdvancedCrawler", "src.data_trans.crawlers.base_crawler.CrawlResult"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_should_retry_result of AdvancedCrawler", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_user_agent_manager": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "src.data_trans.crawlers.advanced_crawler.AdvancedCrawler._user_agent_manager", "name": "_user_agent_manager", "setter_type": null, "type": {".class": "UnionType", "items": ["src.data_trans.crawlers.user_agent_manager.UserAgentManager", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "cleanup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "src.data_trans.crawlers.advanced_crawler.AdvancedCrawler.cleanup", "name": "cleanup", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["src.data_trans.crawlers.advanced_crawler.AdvancedCrawler"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "cleanup of AdvancedCrawler", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "src.data_trans.crawlers.advanced_crawler.AdvancedCrawler.config", "name": "config", "setter_type": null, "type": "src.data_trans.crawlers.advanced_crawler.AdvancedCrawlerConfig"}}, "fetch_single": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "url", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "src.data_trans.crawlers.advanced_crawler.AdvancedCrawler.fetch_single", "name": "fetch_single", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "url", "kwargs"], "arg_types": ["src.data_trans.crawlers.advanced_crawler.AdvancedCrawler", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "fetch_single of AdvancedCrawler", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "src.data_trans.crawlers.base_crawler.CrawlResult"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "src.data_trans.crawlers.advanced_crawler.AdvancedCrawler.setup", "name": "setup", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["src.data_trans.crawlers.advanced_crawler.AdvancedCrawler"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "setup of AdvancedCrawler", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "stats": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "src.data_trans.crawlers.advanced_crawler.AdvancedCrawler.stats", "name": "stats", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["src.data_trans.crawlers.advanced_crawler.AdvancedCrawler"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "stats of AdvancedCrawler", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "src.data_trans.crawlers.advanced_crawler.AdvancedCrawler.stats", "name": "stats", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["src.data_trans.crawlers.advanced_crawler.AdvancedCrawler"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "stats of AdvancedCrawler", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "src.data_trans.crawlers.advanced_crawler.AdvancedCrawler.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "src.data_trans.crawlers.advanced_crawler.AdvancedCrawler", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AdvancedCrawlerConfig": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["src.data_trans.crawlers.base_crawler.CrawlerConfig"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "src.data_trans.crawlers.advanced_crawler.AdvancedCrawlerConfig", "name": "AdvancedCrawlerConfig", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "src.data_trans.crawlers.advanced_crawler.AdvancedCrawlerConfig", "has_param_spec_type": false, "metaclass_type": "pydantic._internal._model_construction.ModelMetaclass", "metadata": {"pydantic-mypy-metadata": {"class_vars": {}, "config": {}, "fields": {"anti_detection_config": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 63, "name": "anti_detection_config", "strict": null, "type": {".class": "UnionType", "items": ["src.data_trans.crawlers.anti_detection.AntiDetectionConfig", {".class": "NoneType"}], "uses_pep604_syntax": false}}, "cache_ttl": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 79, "name": "cache_ttl", "strict": null, "type": "builtins.float"}, "captcha_config": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 57, "name": "captcha_config", "strict": null, "type": {".class": "UnionType", "items": ["src.data_trans.crawlers.captcha_solver.CaptchaConfig", {".class": "NoneType"}], "uses_pep604_syntax": false}}, "default_headers": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 52, "name": "default_headers", "strict": null, "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}, "enable_anti_detection": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 62, "name": "enable_anti_detection", "strict": null, "type": "builtins.bool"}, "enable_captcha_solving": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 56, "name": "enable_captcha_solving", "strict": null, "type": "builtins.bool"}, "enable_js_rendering": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 50, "name": "enable_js_rendering", "strict": null, "type": "builtins.bool"}, "enable_proxy_pool": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 36, "name": "enable_proxy_pool", "strict": null, "type": "builtins.bool"}, "enable_response_caching": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 78, "name": "enable_response_caching", "strict": null, "type": "builtins.bool"}, "enable_session_management": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 68, "name": "enable_session_management", "strict": null, "type": "builtins.bool"}, "enable_smart_retry": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 74, "name": "enable_smart_retry", "strict": null, "type": "builtins.bool"}, "enable_user_agent_rotation": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 42, "name": "enable_user_agent_rotation", "strict": null, "type": "builtins.bool"}, "js_renderer_config": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 51, "name": "js_renderer_config", "strict": null, "type": {".class": "UnionType", "items": ["src.data_trans.crawlers.js_renderer.JSRendererConfig", {".class": "NoneType"}], "uses_pep604_syntax": false}}, "max_concurrent_requests": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 43, "name": "max_concurrent_requests", "strict": null, "type": "builtins.int"}, "max_retries": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 39, "name": "max_retries", "strict": null, "type": "builtins.int"}, "proxy_enabled": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 47, "name": "proxy_enabled", "strict": null, "type": "builtins.bool"}, "proxy_list": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 48, "name": "proxy_list", "strict": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}, "proxy_pool_config": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 37, "name": "proxy_pool_config", "strict": null, "type": {".class": "UnionType", "items": ["src.data_trans.crawlers.proxy_pool.ProxyPoolConfig", {".class": "NoneType"}], "uses_pep604_syntax": false}}, "proxy_rotation": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 49, "name": "proxy_rotation", "strict": null, "type": "builtins.bool"}, "rate_limit_per_second": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 44, "name": "rate_limit_per_second", "strict": null, "type": "builtins.float"}, "retry_delay": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 40, "name": "retry_delay", "strict": null, "type": "builtins.float"}, "session_config": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 69, "name": "session_config", "strict": null, "type": {".class": "UnionType", "items": ["src.data_trans.crawlers.session_manager.SessionConfig", {".class": "NoneType"}], "uses_pep604_syntax": false}}, "smart_retry_max_attempts": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 75, "name": "smart_retry_max_attempts", "strict": null, "type": "builtins.int"}, "timeout": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 38, "name": "timeout", "strict": null, "type": "builtins.float"}, "user_agent": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 34, "name": "user_agent", "strict": null, "type": "builtins.str"}, "user_agent_config": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 45, "name": "user_agent_config", "strict": null, "type": {".class": "UnionType", "items": ["src.data_trans.crawlers.user_agent_manager.UserAgentConfig", {".class": "NoneType"}], "uses_pep604_syntax": false}}}}}, "module_name": "src.data_trans.crawlers.advanced_crawler", "mro": ["src.data_trans.crawlers.advanced_crawler.AdvancedCrawlerConfig", "src.data_trans.crawlers.base_crawler.CrawlerConfig", "pydantic.main.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["__pydantic_self__", "default_headers", "max_concurrent_requests", "max_retries", "proxy_enabled", "proxy_list", "proxy_rotation", "rate_limit_per_second", "retry_delay", "timeout", "user_agent", "enable_proxy_pool", "proxy_pool_config", "enable_user_agent_rotation", "user_agent_config", "enable_js_rendering", "js_renderer_config", "enable_captcha_solving", "captcha_config", "enable_anti_detection", "anti_detection_config", "enable_session_management", "session_config", "enable_smart_retry", "smart_retry_max_attempts", "enable_response_caching", "cache_ttl"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.data_trans.crawlers.advanced_crawler.AdvancedCrawlerConfig.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["__pydantic_self__", "default_headers", "max_concurrent_requests", "max_retries", "proxy_enabled", "proxy_list", "proxy_rotation", "rate_limit_per_second", "retry_delay", "timeout", "user_agent", "enable_proxy_pool", "proxy_pool_config", "enable_user_agent_rotation", "user_agent_config", "enable_js_rendering", "js_renderer_config", "enable_captcha_solving", "captcha_config", "enable_anti_detection", "anti_detection_config", "enable_session_management", "session_config", "enable_smart_retry", "smart_retry_max_attempts", "enable_response_caching", "cache_ttl"], "arg_types": ["src.data_trans.crawlers.advanced_crawler.AdvancedCrawlerConfig", {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.int", "builtins.int", "builtins.bool", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.bool", "builtins.float", "builtins.float", "builtins.float", "builtins.str", "builtins.bool", {".class": "UnionType", "items": ["src.data_trans.crawlers.proxy_pool.ProxyPoolConfig", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["src.data_trans.crawlers.user_agent_manager.UserAgentConfig", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["src.data_trans.crawlers.js_renderer.JSRendererConfig", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["src.data_trans.crawlers.captcha_solver.CaptchaConfig", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["src.data_trans.crawlers.anti_detection.AntiDetectionConfig", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["src.data_trans.crawlers.session_manager.SessionConfig", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.int", "builtins.bool", "builtins.float"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of AdvancedCrawlerConfig", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "anti_detection_config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.crawlers.advanced_crawler.AdvancedCrawlerConfig.anti_detection_config", "name": "anti_detection_config", "setter_type": null, "type": {".class": "UnionType", "items": ["src.data_trans.crawlers.anti_detection.AntiDetectionConfig", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "cache_ttl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.crawlers.advanced_crawler.AdvancedCrawlerConfig.cache_ttl", "name": "cache_ttl", "setter_type": null, "type": "builtins.float"}}, "captcha_config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.crawlers.advanced_crawler.AdvancedCrawlerConfig.captcha_config", "name": "captcha_config", "setter_type": null, "type": {".class": "UnionType", "items": ["src.data_trans.crawlers.captcha_solver.CaptchaConfig", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "default_headers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.crawlers.advanced_crawler.AdvancedCrawlerConfig.default_headers", "name": "default_headers", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "enable_anti_detection": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.crawlers.advanced_crawler.AdvancedCrawlerConfig.enable_anti_detection", "name": "enable_anti_detection", "setter_type": null, "type": "builtins.bool"}}, "enable_captcha_solving": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.crawlers.advanced_crawler.AdvancedCrawlerConfig.enable_captcha_solving", "name": "enable_captcha_solving", "setter_type": null, "type": "builtins.bool"}}, "enable_js_rendering": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.crawlers.advanced_crawler.AdvancedCrawlerConfig.enable_js_rendering", "name": "enable_js_rendering", "setter_type": null, "type": "builtins.bool"}}, "enable_proxy_pool": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.crawlers.advanced_crawler.AdvancedCrawlerConfig.enable_proxy_pool", "name": "enable_proxy_pool", "setter_type": null, "type": "builtins.bool"}}, "enable_response_caching": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.crawlers.advanced_crawler.AdvancedCrawlerConfig.enable_response_caching", "name": "enable_response_caching", "setter_type": null, "type": "builtins.bool"}}, "enable_session_management": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.crawlers.advanced_crawler.AdvancedCrawlerConfig.enable_session_management", "name": "enable_session_management", "setter_type": null, "type": "builtins.bool"}}, "enable_smart_retry": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.crawlers.advanced_crawler.AdvancedCrawlerConfig.enable_smart_retry", "name": "enable_smart_retry", "setter_type": null, "type": "builtins.bool"}}, "enable_user_agent_rotation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.crawlers.advanced_crawler.AdvancedCrawlerConfig.enable_user_agent_rotation", "name": "enable_user_agent_rotation", "setter_type": null, "type": "builtins.bool"}}, "js_renderer_config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.crawlers.advanced_crawler.AdvancedCrawlerConfig.js_renderer_config", "name": "js_renderer_config", "setter_type": null, "type": {".class": "UnionType", "items": ["src.data_trans.crawlers.js_renderer.JSRendererConfig", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "max_concurrent_requests": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.crawlers.advanced_crawler.AdvancedCrawlerConfig.max_concurrent_requests", "name": "max_concurrent_requests", "setter_type": null, "type": "builtins.int"}}, "max_retries": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.crawlers.advanced_crawler.AdvancedCrawlerConfig.max_retries", "name": "max_retries", "setter_type": null, "type": "builtins.int"}}, "model_construct": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": [null, "_fields_set", "default_headers", "max_concurrent_requests", "max_retries", "proxy_enabled", "proxy_list", "proxy_rotation", "rate_limit_per_second", "retry_delay", "timeout", "user_agent", "enable_proxy_pool", "proxy_pool_config", "enable_user_agent_rotation", "user_agent_config", "enable_js_rendering", "js_renderer_config", "enable_captcha_solving", "captcha_config", "enable_anti_detection", "anti_detection_config", "enable_session_management", "session_config", "enable_smart_retry", "smart_retry_max_attempts", "enable_response_caching", "cache_ttl"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "src.data_trans.crawlers.advanced_crawler.AdvancedCrawlerConfig.model_construct", "name": "model_construct", "type": {".class": "CallableType", "arg_kinds": [0, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["_cls", "_fields_set", "default_headers", "max_concurrent_requests", "max_retries", "proxy_enabled", "proxy_list", "proxy_rotation", "rate_limit_per_second", "retry_delay", "timeout", "user_agent", "enable_proxy_pool", "proxy_pool_config", "enable_user_agent_rotation", "user_agent_config", "enable_js_rendering", "js_renderer_config", "enable_captcha_solving", "captcha_config", "enable_anti_detection", "anti_detection_config", "enable_session_management", "session_config", "enable_smart_retry", "smart_retry_max_attempts", "enable_response_caching", "cache_ttl"], "arg_types": [{".class": "TypeType", "item": "src.data_trans.crawlers.advanced_crawler.AdvancedCrawlerConfig"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.int", "builtins.int", "builtins.bool", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.bool", "builtins.float", "builtins.float", "builtins.float", "builtins.str", "builtins.bool", {".class": "UnionType", "items": ["src.data_trans.crawlers.proxy_pool.ProxyPoolConfig", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["src.data_trans.crawlers.user_agent_manager.UserAgentConfig", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["src.data_trans.crawlers.js_renderer.JSRendererConfig", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["src.data_trans.crawlers.captcha_solver.CaptchaConfig", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["src.data_trans.crawlers.anti_detection.AntiDetectionConfig", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["src.data_trans.crawlers.session_manager.SessionConfig", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.int", "builtins.bool", "builtins.float"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "model_construct of AdvancedCrawlerConfig", "ret_type": "src.data_trans.crawlers.advanced_crawler.AdvancedCrawlerConfig", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready"], "fullname": "src.data_trans.crawlers.advanced_crawler.AdvancedCrawlerConfig.model_construct", "name": "model_construct", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["_cls", "_fields_set", "default_headers", "max_concurrent_requests", "max_retries", "proxy_enabled", "proxy_list", "proxy_rotation", "rate_limit_per_second", "retry_delay", "timeout", "user_agent", "enable_proxy_pool", "proxy_pool_config", "enable_user_agent_rotation", "user_agent_config", "enable_js_rendering", "js_renderer_config", "enable_captcha_solving", "captcha_config", "enable_anti_detection", "anti_detection_config", "enable_session_management", "session_config", "enable_smart_retry", "smart_retry_max_attempts", "enable_response_caching", "cache_ttl"], "arg_types": [{".class": "TypeType", "item": "src.data_trans.crawlers.advanced_crawler.AdvancedCrawlerConfig"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.int", "builtins.int", "builtins.bool", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.bool", "builtins.float", "builtins.float", "builtins.float", "builtins.str", "builtins.bool", {".class": "UnionType", "items": ["src.data_trans.crawlers.proxy_pool.ProxyPoolConfig", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["src.data_trans.crawlers.user_agent_manager.UserAgentConfig", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["src.data_trans.crawlers.js_renderer.JSRendererConfig", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["src.data_trans.crawlers.captcha_solver.CaptchaConfig", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["src.data_trans.crawlers.anti_detection.AntiDetectionConfig", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["src.data_trans.crawlers.session_manager.SessionConfig", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.int", "builtins.bool", "builtins.float"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "model_construct of AdvancedCrawlerConfig", "ret_type": "src.data_trans.crawlers.advanced_crawler.AdvancedCrawlerConfig", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "proxy_enabled": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.crawlers.advanced_crawler.AdvancedCrawlerConfig.proxy_enabled", "name": "proxy_enabled", "setter_type": null, "type": "builtins.bool"}}, "proxy_list": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.crawlers.advanced_crawler.AdvancedCrawlerConfig.proxy_list", "name": "proxy_list", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "proxy_pool_config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.crawlers.advanced_crawler.AdvancedCrawlerConfig.proxy_pool_config", "name": "proxy_pool_config", "setter_type": null, "type": {".class": "UnionType", "items": ["src.data_trans.crawlers.proxy_pool.ProxyPoolConfig", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "proxy_rotation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.crawlers.advanced_crawler.AdvancedCrawlerConfig.proxy_rotation", "name": "proxy_rotation", "setter_type": null, "type": "builtins.bool"}}, "rate_limit_per_second": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.crawlers.advanced_crawler.AdvancedCrawlerConfig.rate_limit_per_second", "name": "rate_limit_per_second", "setter_type": null, "type": "builtins.float"}}, "retry_delay": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.crawlers.advanced_crawler.AdvancedCrawlerConfig.retry_delay", "name": "retry_delay", "setter_type": null, "type": "builtins.float"}}, "session_config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.crawlers.advanced_crawler.AdvancedCrawlerConfig.session_config", "name": "session_config", "setter_type": null, "type": {".class": "UnionType", "items": ["src.data_trans.crawlers.session_manager.SessionConfig", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "smart_retry_max_attempts": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.crawlers.advanced_crawler.AdvancedCrawlerConfig.smart_retry_max_attempts", "name": "smart_retry_max_attempts", "setter_type": null, "type": "builtins.int"}}, "timeout": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.crawlers.advanced_crawler.AdvancedCrawlerConfig.timeout", "name": "timeout", "setter_type": null, "type": "builtins.float"}}, "user_agent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.crawlers.advanced_crawler.AdvancedCrawlerConfig.user_agent", "name": "user_agent", "setter_type": null, "type": "builtins.str"}}, "user_agent_config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.crawlers.advanced_crawler.AdvancedCrawlerConfig.user_agent_config", "name": "user_agent_config", "setter_type": null, "type": {".class": "UnionType", "items": ["src.data_trans.crawlers.user_agent_manager.UserAgentConfig", {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "src.data_trans.crawlers.advanced_crawler.AdvancedCrawlerConfig.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "src.data_trans.crawlers.advanced_crawler.AdvancedCrawlerConfig", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AntiDetection": {".class": "SymbolTableNode", "cross_ref": "src.data_trans.crawlers.anti_detection.AntiDetection", "kind": "Gdef", "module_hidden": true, "module_public": false}, "AntiDetectionConfig": {".class": "SymbolTableNode", "cross_ref": "src.data_trans.crawlers.anti_detection.AntiDetectionConfig", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BaseCrawler": {".class": "SymbolTableNode", "cross_ref": "src.data_trans.crawlers.base_crawler.BaseCrawler", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BaseModel": {".class": "SymbolTableNode", "cross_ref": "pydantic.main.BaseModel", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CaptchaConfig": {".class": "SymbolTableNode", "cross_ref": "src.data_trans.crawlers.captcha_solver.CaptchaConfig", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CaptchaSolver": {".class": "SymbolTableNode", "cross_ref": "src.data_trans.crawlers.captcha_solver.CaptchaSolver", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CrawlResult": {".class": "SymbolTableNode", "cross_ref": "src.data_trans.crawlers.base_crawler.CrawlResult", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CrawlerConfig": {".class": "SymbolTableNode", "cross_ref": "src.data_trans.crawlers.base_crawler.CrawlerConfig", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Field": {".class": "SymbolTableNode", "cross_ref": "pydantic.fields.Field", "kind": "Gdef", "module_hidden": true, "module_public": false}, "JSRenderer": {".class": "SymbolTableNode", "cross_ref": "src.data_trans.crawlers.js_renderer.JSRenderer", "kind": "Gdef", "module_hidden": true, "module_public": false}, "JSRendererConfig": {".class": "SymbolTableNode", "cross_ref": "src.data_trans.crawlers.js_renderer.JSRendererConfig", "kind": "Gdef", "module_hidden": true, "module_public": false}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ProxyPool": {".class": "SymbolTableNode", "cross_ref": "src.data_trans.crawlers.proxy_pool.ProxyPool", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ProxyPoolConfig": {".class": "SymbolTableNode", "cross_ref": "src.data_trans.crawlers.proxy_pool.ProxyPoolConfig", "kind": "Gdef", "module_hidden": true, "module_public": false}, "SessionConfig": {".class": "SymbolTableNode", "cross_ref": "src.data_trans.crawlers.session_manager.SessionConfig", "kind": "Gdef", "module_hidden": true, "module_public": false}, "SessionManager": {".class": "SymbolTableNode", "cross_ref": "src.data_trans.crawlers.session_manager.SessionManager", "kind": "Gdef", "module_hidden": true, "module_public": false}, "UserAgentConfig": {".class": "SymbolTableNode", "cross_ref": "src.data_trans.crawlers.user_agent_manager.UserAgentConfig", "kind": "Gdef", "module_hidden": true, "module_public": false}, "UserAgentManager": {".class": "SymbolTableNode", "cross_ref": "src.data_trans.crawlers.user_agent_manager.UserAgentManager", "kind": "Gdef", "module_hidden": true, "module_public": false}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.crawlers.advanced_crawler.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.crawlers.advanced_crawler.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.crawlers.advanced_crawler.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.crawlers.advanced_crawler.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.crawlers.advanced_crawler.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.crawlers.advanced_crawler.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "asyncio": {".class": "SymbolTableNode", "cross_ref": "asyncio", "kind": "Gdef", "module_hidden": true, "module_public": false}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "src.data_trans.crawlers.advanced_crawler.logger", "name": "logger", "setter_type": null, "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef", "module_hidden": true, "module_public": false}, "time": {".class": "SymbolTableNode", "cross_ref": "time", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "src\\data_trans\\crawlers\\advanced_crawler.py"}