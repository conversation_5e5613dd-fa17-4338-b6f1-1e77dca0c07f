{"data_mtime": 1752408380, "dep_lines": [5, 6, 7, 1, 1, 1, 1, 1, 1, 15], "dep_prios": [10, 10, 5, 5, 30, 30, 30, 30, 30, 5], "dependencies": ["os", "sys", "datetime", "builtins", "_frozen_importlib", "abc", "typing", "ntpath", "posixpath"], "hash": "e6d9328401c8a7260939ffa86344412c4bbee202", "id": "airflow.dags.batch_process_dag", "ignore_all": false, "interface_hash": "4fb110e1ca1924f67747e3ab908b9d57d2132b36", "mtime": 1752421148, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": true, "disable_error_code": [], "disable_memoryview_promotion": true, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": true, "disallow_untyped_calls": true, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": true, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": true, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": false, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": ["pydantic.mypy"], "strict_bytes": true, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": false, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "airflow\\dags\\batch_process_dag.py", "plugin_data": [{"debug_dataclass_transform": false, "init_forbid_extra": true, "init_typed": true, "warn_required_dynamic_aliases": true}, null], "size": 1838, "suppressed": ["data_trans.workflow.dag_templates"], "version_id": "1.16.1"}