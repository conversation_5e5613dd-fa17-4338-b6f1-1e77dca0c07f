"""
反检测模块

提供反爬虫检测功能，包括随机延迟、请求头伪装、行为模拟等。
帮助爬虫绕过常见的反爬虫机制。
"""

import asyncio
import logging
import random
import time
from typing import Any, Dict, List, Optional, Tuple

from pydantic import BaseModel, Field
from selenium import webdriver
from selenium.webdriver.chrome.options import Options as ChromeOptions
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.common.by import By
from selenium_stealth import stealth

from .user_agent_manager import UserAgentConfig, UserAgentManager

logger = logging.getLogger(__name__)


class AntiDetectionConfig(BaseModel):
    """反检测配置"""

    # 延迟配置
    enable_random_delay: bool = Field(default=True, description="启用随机延迟")
    min_delay: float = Field(default=1.0, description="最小延迟时间（秒）")
    max_delay: float = Field(default=5.0, description="最大延迟时间（秒）")

    # 请求头伪装
    enable_header_rotation: bool = Field(default=True, description="启用请求头轮换")
    enable_user_agent_rotation: bool = Field(
        default=True, description="启用User-Agent轮换"
    )

    # 行为模拟
    enable_mouse_simulation: bool = Field(default=False, description="启用鼠标行为模拟")
    enable_keyboard_simulation: bool = Field(
        default=False, description="启用键盘行为模拟"
    )
    enable_scroll_simulation: bool = Field(
        default=False, description="启用滚动行为模拟"
    )

    # 浏览器指纹
    enable_fingerprint_randomization: bool = Field(
        default=True, description="启用浏览器指纹随机化"
    )

    # 会话管理
    enable_session_rotation: bool = Field(default=True, description="启用会话轮换")
    session_lifetime: float = Field(default=300.0, description="会话生命周期（秒）")

    # 请求模式
    enable_request_pattern_randomization: bool = Field(
        default=True, description="启用请求模式随机化"
    )
    burst_requests_enabled: bool = Field(default=False, description="启用突发请求模式")

    # 错误处理
    enable_smart_retry: bool = Field(default=True, description="启用智能重试")
    retry_on_status_codes: List[int] = Field(
        default=[403, 429, 503, 520, 521, 522, 524], description="需要重试的状态码"
    )

    # 检测规避
    enable_webdriver_stealth: bool = Field(
        default=True, description="启用WebDriver隐身模式"
    )
    enable_canvas_fingerprint_protection: bool = Field(
        default=True, description="启用Canvas指纹保护"
    )


class AntiDetection:
    """反检测管理器"""

    def __init__(self, config: AntiDetectionConfig) -> None:
        """初始化反检测管理器

        Args:
            config: 反检测配置
        """
        self.config = config
        self._last_request_time = 0.0
        self._request_count = 0
        self._session_start_time = time.time()

        # 初始化User-Agent管理器
        if self.config.enable_user_agent_rotation:
            ua_config = UserAgentConfig()
            self._ua_manager = UserAgentManager(ua_config)
        else:
            self._ua_manager = None

        # 常见请求头模板
        self._header_templates = [
            {
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
                "Accept-Language": "en-US,en;q=0.5",
                "Accept-Encoding": "gzip, deflate, br",
                "DNT": "1",
                "Connection": "keep-alive",
                "Upgrade-Insecure-Requests": "1",
            },
            {
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
                "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
                "Accept-Encoding": "gzip, deflate, br",
                "Cache-Control": "max-age=0",
                "Connection": "keep-alive",
                "Upgrade-Insecure-Requests": "1",
            },
            {
                "Accept": "application/json, text/plain, */*",
                "Accept-Language": "en-US,en;q=0.9",
                "Accept-Encoding": "gzip, deflate, br",
                "Connection": "keep-alive",
                "Sec-Fetch-Dest": "empty",
                "Sec-Fetch-Mode": "cors",
                "Sec-Fetch-Site": "same-origin",
            },
        ]

    async def apply_delay(self) -> None:
        """应用随机延迟"""
        if not self.config.enable_random_delay:
            return

        current_time = time.time()
        time_since_last = current_time - self._last_request_time

        # 计算延迟时间
        if self.config.enable_request_pattern_randomization:
            # 随机化请求模式
            if random.random() < 0.1:  # 10%概率使用较长延迟
                delay = random.uniform(
                    self.config.max_delay * 2, self.config.max_delay * 4
                )
            elif random.random() < 0.3:  # 30%概率使用较短延迟
                delay = random.uniform(0.1, self.config.min_delay)
            else:
                delay = random.uniform(self.config.min_delay, self.config.max_delay)
        else:
            delay = random.uniform(self.config.min_delay, self.config.max_delay)

        # 确保最小间隔
        min_interval = 0.5
        if time_since_last < min_interval:
            delay = max(delay, min_interval - time_since_last)

        if delay > 0:
            logger.debug(f"应用延迟: {delay:.2f}秒")
            await asyncio.sleep(delay)

        self._last_request_time = time.time()
        self._request_count += 1

    def get_random_headers(
        self, base_headers: Optional[Dict[str, str]] = None
    ) -> Dict[str, str]:
        """获取随机化的请求头

        Args:
            base_headers: 基础请求头

        Returns:
            随机化的请求头
        """
        headers = base_headers.copy() if base_headers else {}

        if self.config.enable_header_rotation:
            # 选择随机请求头模板
            template = random.choice(self._header_templates)
            headers.update(template)

        if self.config.enable_user_agent_rotation and self._ua_manager:
            headers["User-Agent"] = self._ua_manager.get_random_user_agent()

        # 添加随机化的其他头部
        if self.config.enable_fingerprint_randomization:
            headers.update(self._get_randomized_headers())

        return headers

    def _get_randomized_headers(self) -> Dict[str, str]:
        """获取随机化的额外请求头

        Returns:
            随机化的请求头
        """
        headers = {}

        # 随机添加一些可选头部
        optional_headers = {
            "Sec-CH-UA": '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
            "Sec-CH-UA-Mobile": "?0",
            "Sec-CH-UA-Platform": f'"{random.choice(["Windows", "macOS", "Linux"])}"',
            "Sec-Fetch-User": "?1" if random.random() > 0.5 else None,
            "X-Requested-With": "XMLHttpRequest" if random.random() > 0.7 else None,
        }

        for key, value in optional_headers.items():
            if value and random.random() > 0.3:  # 70%概率添加
                headers[key] = value

        return headers

    def should_rotate_session(self) -> bool:
        """检查是否应该轮换会话

        Returns:
            是否需要轮换会话
        """
        if not self.config.enable_session_rotation:
            return False

        current_time = time.time()
        session_age = current_time - self._session_start_time

        # 基于时间的会话轮换
        if session_age > self.config.session_lifetime:
            return True

        # 基于请求数量的会话轮换
        if self._request_count > random.randint(50, 200):
            return True

        return False

    def reset_session(self) -> None:
        """重置会话状态"""
        self._session_start_time = time.time()
        self._request_count = 0
        logger.info("会话已重置")

    def should_retry(self, status_code: int, response_content: str = "") -> bool:
        """检查是否应该重试请求

        Args:
            status_code: HTTP状态码
            response_content: 响应内容

        Returns:
            是否应该重试
        """
        if not self.config.enable_smart_retry:
            return False

        # 基于状态码的重试
        if status_code in self.config.retry_on_status_codes:
            return True

        # 基于响应内容的重试
        blocked_indicators = [
            "blocked",
            "forbidden",
            "access denied",
            "rate limit",
            "too many requests",
            "captcha",
            "verification required",
            "robot",
            "bot detected",
            "suspicious activity",
        ]

        content_lower = response_content.lower()
        for indicator in blocked_indicators:
            if indicator in content_lower:
                return True

        return False

    def get_retry_delay(self, attempt: int) -> float:
        """获取重试延迟时间

        Args:
            attempt: 重试次数

        Returns:
            延迟时间（秒）
        """
        # 指数退避 + 随机抖动
        base_delay = min(2**attempt, 60)  # 最大60秒
        jitter = random.uniform(0.5, 1.5)
        return base_delay * jitter

    def create_stealth_driver(self, headless: bool = True) -> webdriver.Chrome:
        """创建隐身模式的Chrome驱动

        Args:
            headless: 是否无头模式

        Returns:
            Chrome WebDriver实例
        """
        if not self.config.enable_webdriver_stealth:
            raise ValueError("WebDriver隐身模式未启用")

        options = ChromeOptions()

        if headless:
            options.add_argument("--headless")

        # 基础反检测参数
        options.add_argument("--no-sandbox")
        options.add_argument("--disable-setuid-sandbox")
        options.add_argument("--disable-dev-shm-usage")
        options.add_argument("--disable-accelerated-2d-canvas")
        options.add_argument("--no-first-run")
        options.add_argument("--no-zygote")
        options.add_argument("--disable-gpu")
        options.add_argument("--disable-blink-features=AutomationControlled")

        # 禁用图片和CSS（可选）
        prefs = {
            "profile.managed_default_content_settings.images": 2,
            "profile.default_content_setting_values.notifications": 2,
        }
        options.add_experimental_option("prefs", prefs)

        # 排除自动化标识
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.add_experimental_option("useAutomationExtension", False)

        # 创建驱动
        driver = webdriver.Chrome(options=options)

        # 应用stealth模式
        stealth(
            driver,
            languages=["en-US", "en"],
            vendor="Google Inc.",
            platform="Win32",
            webgl_vendor="Intel Inc.",
            renderer="Intel Iris OpenGL Engine",
            fix_hairline=True,
        )

        # 移除webdriver属性
        driver.execute_script(
            "Object.defineProperty(navigator, 'webdriver', {get: () => undefined})"
        )

        return driver

    async def simulate_human_behavior(
        self, driver: webdriver.Chrome, page_url: str
    ) -> None:
        """模拟人类行为

        Args:
            driver: WebDriver实例
            page_url: 页面URL
        """
        if not any(
            [
                self.config.enable_mouse_simulation,
                self.config.enable_keyboard_simulation,
                self.config.enable_scroll_simulation,
            ]
        ):
            return

        try:
            actions = ActionChains(driver)

            # 随机滚动
            if self.config.enable_scroll_simulation:
                await self._simulate_scrolling(driver)

            # 随机鼠标移动
            if self.config.enable_mouse_simulation:
                await self._simulate_mouse_movement(driver, actions)

            # 随机等待
            await asyncio.sleep(random.uniform(1, 3))

        except Exception as e:
            logger.warning(f"模拟人类行为失败: {e}")

    async def _simulate_scrolling(self, driver: webdriver.Chrome) -> None:
        """模拟滚动行为

        Args:
            driver: WebDriver实例
        """
        # 获取页面高度
        page_height = driver.execute_script("return document.body.scrollHeight")
        viewport_height = driver.execute_script("return window.innerHeight")

        # 随机滚动几次
        scroll_count = random.randint(2, 5)

        for _ in range(scroll_count):
            # 随机滚动位置
            scroll_position = random.randint(0, max(0, page_height - viewport_height))

            # 平滑滚动
            driver.execute_script(
                f"window.scrollTo({{top: {scroll_position}, behavior: 'smooth'}});"
            )

            # 随机等待
            await asyncio.sleep(random.uniform(0.5, 2.0))

    async def _simulate_mouse_movement(
        self, driver: webdriver.Chrome, actions: ActionChains
    ) -> None:
        """模拟鼠标移动

        Args:
            driver: WebDriver实例
            actions: ActionChains实例
        """
        # 获取页面尺寸
        viewport_width = driver.execute_script("return window.innerWidth")
        viewport_height = driver.execute_script("return window.innerHeight")

        # 随机移动几次
        move_count = random.randint(2, 4)

        for _ in range(move_count):
            x = random.randint(0, viewport_width)
            y = random.randint(0, viewport_height)

            actions.move_by_offset(x, y)
            actions.perform()

            await asyncio.sleep(random.uniform(0.2, 1.0))

            # 重置actions
            actions = ActionChains(driver)

    @property
    def stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            "request_count": self._request_count,
            "session_age": time.time() - self._session_start_time,
            "last_request_time": self._last_request_time,
            "config": {
                "random_delay_enabled": self.config.enable_random_delay,
                "header_rotation_enabled": self.config.enable_header_rotation,
                "user_agent_rotation_enabled": self.config.enable_user_agent_rotation,
                "session_rotation_enabled": self.config.enable_session_rotation,
                "smart_retry_enabled": self.config.enable_smart_retry,
            },
        }
