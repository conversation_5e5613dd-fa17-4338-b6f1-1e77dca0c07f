"""
高级爬虫模块

整合所有高级功能的爬虫实现，包括：
- 代理池管理
- User-Agent轮换
- JavaScript渲染
- 验证码识别
- 反检测功能
- 会话管理
- 智能重试策略
"""

import asyncio
import logging
import time
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field

from .anti_detection import AntiDetection, AntiDetectionConfig
from .base_crawler import BaseCrawler, CrawlerConfig, CrawlResult
from .captcha_solver import CaptchaConfig, CaptchaSolver
from .js_renderer import JSRenderer, JSRendererConfig
from .proxy_pool import ProxyPool, ProxyPoolConfig
from .session_manager import SessionConfig, SessionManager
from .user_agent_manager import UserAgentConfig, UserAgentManager

logger = logging.getLogger(__name__)


class AdvancedCrawlerConfig(CrawlerConfig):
    """高级爬虫配置"""

    # 代理配置
    enable_proxy_pool: bool = Field(default=False, description="启用代理池")
    proxy_pool_config: Optional[ProxyPoolConfig] = Field(
        default=None, description="代理池配置"
    )

    # User-Agent配置
    enable_user_agent_rotation: bool = Field(
        default=True, description="启用User-Agent轮换"
    )
    user_agent_config: Optional[UserAgentConfig] = Field(
        default=None, description="User-Agent配置"
    )

    # JavaScript渲染配置
    enable_js_rendering: bool = Field(default=False, description="启用JavaScript渲染")
    js_renderer_config: Optional[JSRendererConfig] = Field(
        default=None, description="JavaScript渲染配置"
    )

    # 验证码识别配置
    enable_captcha_solving: bool = Field(default=False, description="启用验证码识别")
    captcha_config: Optional[CaptchaConfig] = Field(
        default=None, description="验证码配置"
    )

    # 反检测配置
    enable_anti_detection: bool = Field(default=True, description="启用反检测")
    anti_detection_config: Optional[AntiDetectionConfig] = Field(
        default=None, description="反检测配置"
    )

    # 会话管理配置
    enable_session_management: bool = Field(default=True, description="启用会话管理")
    session_config: Optional[SessionConfig] = Field(
        default=None, description="会话配置"
    )

    # 智能重试配置
    enable_smart_retry: bool = Field(default=True, description="启用智能重试")
    smart_retry_max_attempts: int = Field(default=5, description="智能重试最大次数")

    # 性能配置
    enable_response_caching: bool = Field(default=False, description="启用响应缓存")
    cache_ttl: float = Field(default=300.0, description="缓存TTL（秒）")


class AdvancedCrawler(BaseCrawler):
    """高级爬虫实现"""

    def __init__(self, config: AdvancedCrawlerConfig) -> None:
        """初始化高级爬虫

        Args:
            config: 高级爬虫配置
        """
        super().__init__(config)
        self.config: AdvancedCrawlerConfig = config

        # 初始化各个组件
        self._proxy_pool: Optional[ProxyPool] = None
        self._user_agent_manager: Optional[UserAgentManager] = None
        self._js_renderer: Optional[JSRenderer] = None
        self._captcha_solver: Optional[CaptchaSolver] = None
        self._anti_detection: Optional[AntiDetection] = None
        self._session_manager: Optional[SessionManager] = None

        # 响应缓存
        self._response_cache: Dict[str, Any] = {}

    async def setup(self) -> None:
        """设置高级爬虫"""
        # 初始化代理池
        if self.config.enable_proxy_pool and self.config.proxy_pool_config:
            self._proxy_pool = ProxyPool(self.config.proxy_pool_config)
            await self._proxy_pool.start()
            logger.info("代理池已启动")

        # 初始化User-Agent管理器
        if self.config.enable_user_agent_rotation:
            ua_config = self.config.user_agent_config or UserAgentConfig()
            self._user_agent_manager = UserAgentManager(ua_config)
            logger.info("User-Agent管理器已启动")

        # 初始化JavaScript渲染器
        if self.config.enable_js_rendering and self.config.js_renderer_config:
            self._js_renderer = JSRenderer(self.config.js_renderer_config)
            await self._js_renderer.start()
            logger.info("JavaScript渲染器已启动")

        # 初始化验证码识别器
        if self.config.enable_captcha_solving and self.config.captcha_config:
            self._captcha_solver = CaptchaSolver(self.config.captcha_config)
            await self._captcha_solver.start()
            logger.info("验证码识别器已启动")

        # 初始化反检测
        if self.config.enable_anti_detection:
            anti_detection_config = (
                self.config.anti_detection_config or AntiDetectionConfig()
            )
            self._anti_detection = AntiDetection(anti_detection_config)
            logger.info("反检测功能已启动")

        # 初始化会话管理器
        if self.config.enable_session_management:
            session_config = self.config.session_config or SessionConfig()
            self._session_manager = SessionManager(session_config)
            await self._session_manager.start()
            logger.info("会话管理器已启动")

        logger.info("高级爬虫已初始化完成")

    async def cleanup(self) -> None:
        """清理高级爬虫资源"""
        # 停止各个组件
        if self._proxy_pool:
            await self._proxy_pool.stop()

        if self._js_renderer:
            await self._js_renderer.stop()

        if self._captcha_solver:
            await self._captcha_solver.stop()

        if self._session_manager:
            await self._session_manager.stop()

        # 清理缓存
        self._response_cache.clear()

        logger.info("高级爬虫资源已清理")

    async def fetch_single(self, url: str, **kwargs: Any) -> CrawlResult:
        """爬取单个URL

        Args:
            url: 要爬取的URL
            **kwargs: 额外参数

        Returns:
            爬取结果
        """
        # 检查缓存
        if self.config.enable_response_caching:
            cached_result = self._get_cached_response(url)
            if cached_result:
                return cached_result

        # 应用反检测延迟
        if self._anti_detection:
            await self._anti_detection.apply_delay()

        # 智能重试
        if self.config.enable_smart_retry:
            return await self._fetch_with_smart_retry(url, **kwargs)
        else:
            return await self._fetch_single_internal(url, **kwargs)

    async def _fetch_single_internal(self, url: str, **kwargs: Any) -> CrawlResult:
        """内部单次爬取方法

        Args:
            url: 要爬取的URL
            **kwargs: 额外参数

        Returns:
            爬取结果
        """
        start_time = time.time()

        try:
            # 准备请求参数
            request_params = await self._prepare_request_params(url, **kwargs)

            # 选择爬取方式
            if self.config.enable_js_rendering and self._js_renderer:
                # 使用JavaScript渲染
                result = await self._fetch_with_js_rendering(url, request_params)
            else:
                # 使用HTTP客户端
                result = await self._fetch_with_http_client(url, request_params)

            # 处理验证码
            if self._needs_captcha_solving(result):
                result = await self._handle_captcha(url, result, request_params)

            # 缓存响应
            if self.config.enable_response_caching and not result.error:
                self._cache_response(url, result)

            # 报告结果
            await self._report_request_result(
                request_params.get("session_id"), not result.error
            )

            return result

        except Exception as e:
            duration = time.time() - start_time
            logger.error(f"爬取 {url} 失败: {e}")

            # 报告失败
            session_id = kwargs.get("session_id")
            if session_id:
                await self._report_request_result(session_id, False)

            return CrawlResult(
                url=url, data={}, status_code=0, duration=duration, error=str(e)
            )

    async def _prepare_request_params(self, url: str, **kwargs: Any) -> Dict[str, Any]:
        """准备请求参数

        Args:
            url: 请求URL
            **kwargs: 额外参数

        Returns:
            请求参数
        """
        params = kwargs.copy()

        # 获取会话
        if self._session_manager:
            session_info = await self._session_manager.get_session()
            if session_info:
                session_id, client, semaphore = session_info
                params.update(
                    {"session_id": session_id, "client": client, "semaphore": semaphore}
                )

        # 获取代理
        if self._proxy_pool:
            proxy = await self._proxy_pool.get_proxy()
            if proxy:
                params["proxy"] = proxy

        # 获取User-Agent
        if self._user_agent_manager:
            user_agent = self._user_agent_manager.get_random_user_agent()
            params["user_agent"] = user_agent

        # 获取随机化请求头
        if self._anti_detection:
            headers = self._anti_detection.get_random_headers(params.get("headers", {}))
            params["headers"] = headers

        return params

    async def _fetch_with_http_client(
        self, url: str, params: Dict[str, Any]
    ) -> CrawlResult:
        """使用HTTP客户端爬取

        Args:
            url: 请求URL
            params: 请求参数

        Returns:
            爬取结果
        """
        start_time = time.time()

        # 获取HTTP客户端
        client = params.get("client")
        if not client:
            # 如果没有会话管理器，创建临时客户端
            import httpx

            client = httpx.AsyncClient(timeout=httpx.Timeout(self.config.timeout))

        # 获取信号量
        semaphore = params.get("semaphore")

        try:
            # 使用信号量控制并发
            if semaphore:
                async with semaphore:
                    response = await self._make_http_request(client, url, params)
            else:
                response = await self._make_http_request(client, url, params)

            # 解析响应
            data = await self._parse_response(response, url)

            duration = time.time() - start_time

            return CrawlResult(
                url=url,
                data=data,
                status_code=response.status_code,
                headers=dict(response.headers),
                duration=duration,
            )

        except Exception as e:
            duration = time.time() - start_time
            return CrawlResult(
                url=url, data={}, status_code=0, duration=duration, error=str(e)
            )
        finally:
            # 如果是临时客户端，关闭它
            if not params.get("client"):
                await client.aclose()

    async def _make_http_request(self, client, url: str, params: Dict[str, Any]):
        """发送HTTP请求

        Args:
            client: HTTP客户端
            url: 请求URL
            params: 请求参数

        Returns:
            HTTP响应
        """
        request_kwargs = {
            "method": params.get("method", "GET"),
            "url": url,
            "headers": params.get("headers", {}),
            "params": params.get("query_params"),
            "data": params.get("data"),
            "json": params.get("json"),
            "cookies": params.get("cookies"),
        }

        # 设置代理
        proxy = params.get("proxy")
        if proxy:
            request_kwargs["proxy"] = proxy.url

        return await client.request(**request_kwargs)

    async def _fetch_with_js_rendering(
        self, url: str, params: Dict[str, Any]
    ) -> CrawlResult:
        """使用JavaScript渲染爬取

        Args:
            url: 请求URL
            params: 请求参数

        Returns:
            爬取结果
        """
        if not self._js_renderer:
            raise RuntimeError("JavaScript渲染器未初始化")

        # 使用JavaScript渲染器
        render_result = await self._js_renderer.render(url, **params)

        # 转换为CrawlResult
        return CrawlResult(
            url=render_result.url,
            data={"html": render_result.html, "title": render_result.title},
            status_code=render_result.status_code,
            duration=render_result.render_time,
            error=render_result.error,
        )

    async def _fetch_with_smart_retry(self, url: str, **kwargs: Any) -> CrawlResult:
        """智能重试爬取

        Args:
            url: 请求URL
            **kwargs: 额外参数

        Returns:
            爬取结果
        """
        last_result = None

        for attempt in range(self.config.smart_retry_max_attempts):
            try:
                result = await self._fetch_single_internal(url, **kwargs)

                # 检查是否需要重试
                if not self._should_retry_result(result):
                    return result

                last_result = result

                # 计算重试延迟
                if self._anti_detection:
                    retry_delay = self._anti_detection.get_retry_delay(attempt)
                else:
                    retry_delay = self.config.retry_delay * (2**attempt)

                logger.info(
                    f"重试爬取 {url}，第 {attempt + 1} 次尝试，延迟 {retry_delay:.2f}秒"
                )
                await asyncio.sleep(retry_delay)

                # 如果是会话问题，轮换会话
                if self._session_manager and result.status_code in [403, 429]:
                    session_id = kwargs.get("session_id")
                    if session_id:
                        await self._session_manager.invalidate_session(session_id)

            except Exception as e:
                logger.error(f"重试爬取 {url} 异常: {e}")
                last_result = CrawlResult(
                    url=url, data={}, status_code=0, duration=0.0, error=str(e)
                )

        return last_result or CrawlResult(
            url=url, data={}, status_code=0, duration=0.0, error="智能重试失败"
        )

    def _should_retry_result(self, result: CrawlResult) -> bool:
        """检查结果是否需要重试

        Args:
            result: 爬取结果

        Returns:
            是否需要重试
        """
        if result.error:
            return True

        if self._anti_detection:
            return self._anti_detection.should_retry(
                result.status_code, str(result.data)
            )

        # 默认重试条件
        return result.status_code in [403, 429, 503, 520, 521, 522, 524]

    def _needs_captcha_solving(self, result: CrawlResult) -> bool:
        """检查是否需要验证码识别

        Args:
            result: 爬取结果

        Returns:
            是否需要验证码识别
        """
        if not self.config.enable_captcha_solving or not self._captcha_solver:
            return False

        # 检查状态码
        if result.status_code == 403:
            return True

        # 检查响应内容
        content = str(result.data).lower()
        captcha_indicators = ["captcha", "verification", "robot", "human"]

        return any(indicator in content for indicator in captcha_indicators)

    async def _handle_captcha(
        self, url: str, result: CrawlResult, params: Dict[str, Any]
    ) -> CrawlResult:
        """处理验证码

        Args:
            url: 请求URL
            result: 当前结果
            params: 请求参数

        Returns:
            处理后的结果
        """
        if not self._captcha_solver:
            return result

        try:
            # 这里简化处理，实际需要根据具体验证码类型实现
            logger.info(f"检测到验证码，URL: {url}")

            # 尝试识别验证码（这里需要根据实际情况实现）
            # captcha_result = await self._captcha_solver.solve("image", image_data=...)

            # 如果识别成功，重新请求
            # if captcha_result.success:
            #     return await self._fetch_single_internal(url, **params)

            return result

        except Exception as e:
            logger.error(f"处理验证码失败: {e}")
            return result

    async def _parse_response(self, response, url: str) -> Dict[str, Any]:
        """解析响应

        Args:
            response: HTTP响应
            url: 请求URL

        Returns:
            解析后的数据
        """
        content_type = response.headers.get("content-type", "").lower()

        if "application/json" in content_type:
            try:
                return response.json()
            except Exception:
                return {"raw_content": response.text}
        elif "text/html" in content_type:
            return {"html": response.text, "url": url}
        else:
            return {"raw_content": response.text, "content_type": content_type}

    def _get_cached_response(self, url: str) -> Optional[CrawlResult]:
        """获取缓存的响应

        Args:
            url: 请求URL

        Returns:
            缓存的结果，如果不存在或过期则返回None
        """
        if url in self._response_cache:
            cached_data = self._response_cache[url]
            if time.time() - cached_data["timestamp"] < self.config.cache_ttl:
                return cached_data["result"]
            else:
                del self._response_cache[url]

        return None

    def _cache_response(self, url: str, result: CrawlResult) -> None:
        """缓存响应

        Args:
            url: 请求URL
            result: 爬取结果
        """
        self._response_cache[url] = {"result": result, "timestamp": time.time()}

    async def _report_request_result(
        self, session_id: Optional[str], success: bool
    ) -> None:
        """报告请求结果

        Args:
            session_id: 会话ID
            success: 是否成功
        """
        if self._session_manager and session_id:
            await self._session_manager.report_request_result(session_id, success)

    @property
    def stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        stats = {
            "cache_size": len(self._response_cache),
            "components": {
                "proxy_pool": self._proxy_pool.stats if self._proxy_pool else None,
                "user_agent_manager": (
                    self._user_agent_manager.stats if self._user_agent_manager else None
                ),
                "session_manager": (
                    self._session_manager.stats if self._session_manager else None
                ),
                "anti_detection": (
                    self._anti_detection.stats if self._anti_detection else None
                ),
            },
        }

        return stats
