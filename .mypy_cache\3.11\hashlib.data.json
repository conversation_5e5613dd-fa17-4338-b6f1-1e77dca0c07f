{".class": "MypyFile", "_fullname": "<PERSON><PERSON><PERSON>", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "AbstractSet": {".class": "SymbolTableNode", "cross_ref": "typing.AbstractSet", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "HASH": {".class": "SymbolTableNode", "cross_ref": "_hashlib.HASH", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Protocol": {".class": "SymbolTableNode", "cross_ref": "typing.Protocol", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ReadableBuffer": {".class": "SymbolTableNode", "cross_ref": "_typeshed.ReadableBuffer", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_BytesIOLike": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "hashlib._BytesIOLike", "name": "_BytesIOLike", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_protocol"], "fullname": "hashlib._BytesIOLike", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "<PERSON><PERSON><PERSON>", "mro": ["hashlib._BytesIOLike", "builtins.object"], "names": {".class": "SymbolTable", "getbuffer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "hashlib._BytesIOLike.getbuffer", "name": "getbuffer", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["hashlib._BytesIOLike"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "getbuffer of _BytesIOLike", "ret_type": "typing_extensions.Buffer", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "hashlib._BytesIOLike.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "hashlib._BytesIOLike", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_FileDigestFileObj": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "hashlib._FileDigestFileObj", "name": "_FileDigestFileObj", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_protocol"], "fullname": "hashlib._FileDigestFileObj", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "<PERSON><PERSON><PERSON>", "mro": ["hashlib._FileDigestFileObj", "builtins.object"], "names": {".class": "SymbolTable", "readable": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "hashlib._FileDigestFileObj.readable", "name": "readable", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["hashlib._FileDigestFileObj"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "readable of _FileDigestFileObj", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "readinto": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "hashlib._FileDigestFileObj.readinto", "name": "readinto", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["hashlib._FileDigestFileObj", "builtins.bytearray"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "readinto of _FileDigestFileObj", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "hashlib._FileDigestFileObj.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "hashlib._FileDigestFileObj", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_Hash": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "hashlib._Hash", "line": 105, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "_hashlib.HASH"}}, "_HashObject": {".class": "SymbolTableNode", "cross_ref": "_hashlib._HashObject", "kind": "Gdef", "module_hidden": true, "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "hashlib.__all__", "name": "__all__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "hashlib.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "hashlib.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "hashlib.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "hashlib.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "hashlib.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "hashlib.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "algorithms_available": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "hashlib.algorithms_available", "name": "algorithms_available", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.AbstractSet"}}}, "algorithms_guaranteed": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "hashlib.algorithms_guaranteed", "name": "algorithms_guaranteed", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.AbstractSet"}}}, "blake2b": {".class": "SymbolTableNode", "cross_ref": "_blake2.blake2b", "kind": "Gdef"}, "blake2s": {".class": "SymbolTableNode", "cross_ref": "_blake2.blake2s", "kind": "Gdef"}, "file_digest": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5], "arg_names": [null, null, "_bufsize"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "hashlib.file_digest", "name": "file_digest", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": [null, null, "_bufsize"], "arg_types": [{".class": "UnionType", "items": ["hashlib._BytesIOLike", "hashlib._FileDigestFileObj"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "_hashlib._HashObject", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": true}, "builtins.int"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "file_digest", "ret_type": "_hashlib.HASH", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "md5": {".class": "SymbolTableNode", "cross_ref": "_hashlib.openssl_md5", "kind": "Gdef"}, "new": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 5], "arg_names": ["name", "data", "usedforsecurity"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "hashlib.new", "name": "new", "type": {".class": "CallableType", "arg_kinds": [0, 1, 5], "arg_names": ["name", "data", "usedforsecurity"], "arg_types": ["builtins.str", "typing_extensions.Buffer", "builtins.bool"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "new", "ret_type": "_hashlib.HASH", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pbkdf2_hmac": {".class": "SymbolTableNode", "cross_ref": "_hashlib.pbkdf2_hmac", "kind": "Gdef"}, "scrypt": {".class": "SymbolTableNode", "cross_ref": "_hashlib.scrypt", "kind": "Gdef", "module_public": false}, "sha1": {".class": "SymbolTableNode", "cross_ref": "_hashlib.openssl_sha1", "kind": "Gdef"}, "sha224": {".class": "SymbolTableNode", "cross_ref": "_hashlib.openssl_sha224", "kind": "Gdef"}, "sha256": {".class": "SymbolTableNode", "cross_ref": "_hashlib.openssl_sha256", "kind": "Gdef"}, "sha384": {".class": "SymbolTableNode", "cross_ref": "_hashlib.openssl_sha384", "kind": "Gdef"}, "sha3_224": {".class": "SymbolTableNode", "cross_ref": "_hashlib.openssl_sha3_224", "kind": "Gdef"}, "sha3_256": {".class": "SymbolTableNode", "cross_ref": "_hashlib.openssl_sha3_256", "kind": "Gdef"}, "sha3_384": {".class": "SymbolTableNode", "cross_ref": "_hashlib.openssl_sha3_384", "kind": "Gdef"}, "sha3_512": {".class": "SymbolTableNode", "cross_ref": "_hashlib.openssl_sha3_512", "kind": "Gdef"}, "sha512": {".class": "SymbolTableNode", "cross_ref": "_hashlib.openssl_sha512", "kind": "Gdef"}, "shake_128": {".class": "SymbolTableNode", "cross_ref": "_hashlib.openssl_shake_128", "kind": "Gdef"}, "shake_256": {".class": "SymbolTableNode", "cross_ref": "_hashlib.openssl_shake_256", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_hidden": true, "module_public": false}, "type_check_only": {".class": "SymbolTableNode", "cross_ref": "typing.type_check_only", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "C:\\Users\\<USER>\\.cache\\pre-commit\\repoyp9f_v0e\\py_env-python3.11\\Lib\\site-packages\\mypy\\typeshed\\stdlib\\hashlib.pyi"}