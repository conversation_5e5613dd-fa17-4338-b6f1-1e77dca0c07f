{"data_mtime": 1752423502, "dep_lines": [17, 21, 22, 23, 24, 25, 26, 8, 9, 10, 11, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 5, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["src.data_trans.crawlers.advanced_crawler", "src.data_trans.crawlers.anti_detection", "src.data_trans.crawlers.captcha_solver", "src.data_trans.crawlers.js_renderer", "src.data_trans.crawlers.proxy_pool", "src.data_trans.crawlers.session_manager", "src.data_trans.crawlers.user_agent_manager", "asyncio", "logging", "sys", "pathlib", "builtins", "_frozen_importlib", "_typeshed", "abc", "os", "typing", "typing_extensions"], "hash": "c7b2935d8104523062cdf72592dedbc0047169f3", "id": "examples.advanced_crawler_example", "ignore_all": false, "interface_hash": "78d6d52f7a9ca0aca1dea60b1c8b16e55e950ae7", "mtime": 1752423500, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": true, "disable_error_code": [], "disable_memoryview_promotion": true, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": true, "disallow_untyped_calls": true, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": true, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": true, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": false, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": ["pydantic.mypy"], "strict_bytes": true, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": false, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "examples\\advanced_crawler_example.py", "plugin_data": [{"debug_dataclass_transform": false, "init_forbid_extra": true, "init_typed": true, "warn_required_dynamic_aliases": true}, null], "size": 10452, "suppressed": [], "version_id": "1.16.1"}