"""
高级爬虫功能测试

测试高级爬虫的各项功能，包括代理池、User-Agent轮换、
JavaScript渲染、验证码识别、反检测等功能。
"""

import asyncio
import os
import sys
from pathlib import Path
from unittest.mock import AsyncMock, MagicMock, patch

import pytest

from src.data_trans.crawlers.advanced_crawler import (
    AdvancedCrawler,
    AdvancedCrawlerConfig,
)
from src.data_trans.crawlers.anti_detection import AntiDetectionConfig
from src.data_trans.crawlers.captcha_solver import CaptchaConfig
from src.data_trans.crawlers.js_renderer import JSRendererConfig
from src.data_trans.crawlers.proxy_pool import ProxyPoolConfig
from src.data_trans.crawlers.session_manager import SessionConfig
from src.data_trans.crawlers.user_agent_manager import UserAgentConfig

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


class TestAdvancedCrawler:
    """高级爬虫测试类"""

    @pytest.fixture
    def basic_config(self):
        """基础配置"""
        return AdvancedCrawlerConfig(
            enable_proxy_pool=False,
            enable_user_agent_rotation=True,
            enable_js_rendering=False,
            enable_captcha_solving=False,
            enable_anti_detection=True,
            enable_session_management=True,
        )

    @pytest.fixture
    def full_config(self):
        """完整配置"""
        return AdvancedCrawlerConfig(
            enable_proxy_pool=True,
            proxy_pool_config=ProxyPoolConfig(
                max_proxies=10,
                enable_free_sources=False,  # 测试时禁用免费源
            ),
            enable_user_agent_rotation=True,
            user_agent_config=UserAgentConfig(),
            enable_js_rendering=True,
            js_renderer_config=JSRendererConfig(
                renderer_type="playwright",
                headless=True,
            ),
            enable_captcha_solving=True,
            captcha_config=CaptchaConfig(
                solver_type="2captcha",
                api_key="test_key",
            ),
            enable_anti_detection=True,
            anti_detection_config=AntiDetectionConfig(),
            enable_session_management=True,
            session_config=SessionConfig(max_sessions=5),
        )

    @pytest.mark.asyncio
    async def test_basic_crawler_initialization(self, basic_config):
        """测试基础爬虫初始化"""
        crawler = AdvancedCrawler(basic_config)

        # 模拟组件初始化
        with patch.multiple(
            crawler,
            _user_agent_manager=MagicMock(),
            _anti_detection=MagicMock(),
            _session_manager=AsyncMock(),
        ):
            await crawler.setup()

            assert crawler._user_agent_manager is not None
            assert crawler._anti_detection is not None
            assert crawler._session_manager is not None
            assert crawler._proxy_pool is None  # 未启用
            assert crawler._js_renderer is None  # 未启用
            assert crawler._captcha_solver is None  # 未启用

            await crawler.cleanup()

    @pytest.mark.asyncio
    async def test_user_agent_rotation(self, basic_config):
        """测试User-Agent轮换功能"""
        crawler = AdvancedCrawler(basic_config)

        # 模拟User-Agent管理器
        mock_ua_manager = MagicMock()
        mock_ua_manager.get_random_user_agent.return_value = "Test-Agent/1.0"

        with patch.object(crawler, "_user_agent_manager", mock_ua_manager):
            params = await crawler._prepare_request_params("http://example.com")

            assert "user_agent" in params
            assert params["user_agent"] == "Test-Agent/1.0"
            mock_ua_manager.get_random_user_agent.assert_called_once()

    @pytest.mark.asyncio
    async def test_anti_detection_delay(self, basic_config):
        """测试反检测延迟功能"""
        crawler = AdvancedCrawler(basic_config)

        # 模拟反检测组件
        mock_anti_detection = AsyncMock()
        mock_anti_detection.apply_delay = AsyncMock()
        mock_anti_detection.get_random_headers.return_value = {"User-Agent": "Test"}

        with patch.object(crawler, "_anti_detection", mock_anti_detection):
            with patch.object(crawler, "_fetch_single_internal", AsyncMock()):
                await crawler.fetch_single("http://example.com")

                mock_anti_detection.apply_delay.assert_called_once()

    @pytest.mark.asyncio
    async def test_session_management(self, basic_config):
        """测试会话管理功能"""
        crawler = AdvancedCrawler(basic_config)

        # 模拟会话管理器
        mock_session_manager = AsyncMock()
        mock_client = AsyncMock()
        mock_semaphore = AsyncMock()

        mock_session_manager.get_session.return_value = (
            "session_1",
            mock_client,
            mock_semaphore,
        )
        mock_session_manager.report_request_result = AsyncMock()

        with patch.object(crawler, "_session_manager", mock_session_manager):
            params = await crawler._prepare_request_params("http://example.com")

            assert "session_id" in params
            assert "client" in params
            assert "semaphore" in params
            assert params["session_id"] == "session_1"
            assert params["client"] == mock_client
            assert params["semaphore"] == mock_semaphore

            mock_session_manager.get_session.assert_called_once()

    @pytest.mark.asyncio
    async def test_smart_retry_mechanism(self, basic_config):
        """测试智能重试机制"""
        config = basic_config
        config.enable_smart_retry = True
        config.smart_retry_max_attempts = 3

        crawler = AdvancedCrawler(config)

        # 模拟反检测组件
        mock_anti_detection = MagicMock()
        mock_anti_detection.get_retry_delay.return_value = 0.1  # 快速重试

        # 模拟失败的请求结果
        from src.data_trans.crawlers.base_crawler import CrawlResult

        failed_result = CrawlResult(
            url="http://example.com",
            data={},
            status_code=403,
            duration=1.0,
            error="Forbidden",
        )

        success_result = CrawlResult(
            url="http://example.com",
            data={"success": True},
            status_code=200,
            duration=1.0,
        )

        with patch.object(crawler, "_anti_detection", mock_anti_detection):
            with patch.object(crawler, "_fetch_single_internal") as mock_fetch:
                # 前两次失败，第三次成功
                mock_fetch.side_effect = [failed_result, failed_result, success_result]

                result = await crawler._fetch_with_smart_retry("http://example.com")

                assert result.status_code == 200
                assert result.data == {"success": True}
                assert mock_fetch.call_count == 3

    @pytest.mark.asyncio
    async def test_response_caching(self, basic_config):
        """测试响应缓存功能"""
        config = basic_config
        config.enable_response_caching = True
        config.cache_ttl = 60.0

        crawler = AdvancedCrawler(config)

        from src.data_trans.crawlers.base_crawler import CrawlResult

        cached_result = CrawlResult(
            url="http://example.com",
            data={"cached": True},
            status_code=200,
            duration=1.0,
        )

        # 缓存结果
        crawler._cache_response("http://example.com", cached_result)

        # 获取缓存结果
        result = crawler._get_cached_response("http://example.com")

        assert result is not None
        assert result.data == {"cached": True}
        assert result.status_code == 200

    @pytest.mark.asyncio
    async def test_proxy_pool_integration(self):
        """测试代理池集成"""
        config = AdvancedCrawlerConfig(
            enable_proxy_pool=True,
            proxy_pool_config=ProxyPoolConfig(
                max_proxies=5,
                enable_free_sources=False,
            ),
        )

        crawler = AdvancedCrawler(config)

        # 模拟代理池
        from src.data_trans.crawlers.proxy_pool import ProxyInfo

        mock_proxy = ProxyInfo(host="127.0.0.1", port=8080, protocol="http")

        mock_proxy_pool = AsyncMock()
        mock_proxy_pool.get_proxy.return_value = mock_proxy

        with patch.object(crawler, "_proxy_pool", mock_proxy_pool):
            params = await crawler._prepare_request_params("http://example.com")

            assert "proxy" in params
            assert params["proxy"] == mock_proxy
            mock_proxy_pool.get_proxy.assert_called_once()

    @pytest.mark.asyncio
    async def test_javascript_rendering_integration(self):
        """测试JavaScript渲染集成"""
        config = AdvancedCrawlerConfig(
            enable_js_rendering=True,
            js_renderer_config=JSRendererConfig(
                renderer_type="playwright",
                headless=True,
            ),
        )

        crawler = AdvancedCrawler(config)

        # 模拟JavaScript渲染器
        from src.data_trans.crawlers.js_renderer import JSRenderResult

        mock_render_result = JSRenderResult(
            url="http://example.com",
            html="<html><body>Rendered</body></html>",
            status_code=200,
            title="Test Page",
            render_time=2.0,
        )

        mock_js_renderer = AsyncMock()
        mock_js_renderer.render.return_value = mock_render_result

        with patch.object(crawler, "_js_renderer", mock_js_renderer):
            result = await crawler._fetch_with_js_rendering("http://example.com", {})

            assert result.status_code == 200
            assert "html" in result.data
            assert result.data["html"] == "<html><body>Rendered</body></html>"
            assert result.data["title"] == "Test Page"
            mock_js_renderer.render.assert_called_once()

    @pytest.mark.asyncio
    async def test_captcha_detection(self, basic_config):
        """测试验证码检测"""
        config = basic_config
        config.enable_captcha_solving = True
        config.captcha_config = CaptchaConfig(
            solver_type="2captcha", api_key="test_key"
        )

        crawler = AdvancedCrawler(config)

        from src.data_trans.crawlers.base_crawler import CrawlResult

        # 测试需要验证码的响应
        captcha_result = CrawlResult(
            url="http://example.com",
            data={"html": "Please solve the captcha"},
            status_code=403,
            duration=1.0,
        )

        needs_captcha = crawler._needs_captcha_solving(captcha_result)
        assert needs_captcha is True

        # 测试正常响应
        normal_result = CrawlResult(
            url="http://example.com",
            data={"success": True},
            status_code=200,
            duration=1.0,
        )

        needs_captcha = crawler._needs_captcha_solving(normal_result)
        assert needs_captcha is False

    def test_crawler_stats(self, basic_config):
        """测试爬虫统计信息"""
        crawler = AdvancedCrawler(basic_config)

        # 模拟各组件的统计信息
        mock_proxy_pool = MagicMock()
        mock_proxy_pool.stats = {"total_proxies": 10, "working_proxies": 8}

        mock_ua_manager = MagicMock()
        mock_ua_manager.stats = {"total_cached_user_agents": 50}

        mock_session_manager = MagicMock()
        mock_session_manager.stats = {"total_sessions": 5, "healthy_sessions": 4}

        mock_anti_detection = MagicMock()
        mock_anti_detection.stats = {"request_count": 100}

        crawler._proxy_pool = mock_proxy_pool
        crawler._user_agent_manager = mock_ua_manager
        crawler._session_manager = mock_session_manager
        crawler._anti_detection = mock_anti_detection

        stats = crawler.stats

        assert "cache_size" in stats
        assert "components" in stats
        assert stats["components"]["proxy_pool"]["total_proxies"] == 10
        assert (
            stats["components"]["user_agent_manager"]["total_cached_user_agents"] == 50
        )
        assert stats["components"]["session_manager"]["total_sessions"] == 5
        assert stats["components"]["anti_detection"]["request_count"] == 100


if __name__ == "__main__":
    # 运行基础测试
    async def run_basic_test():
        config = AdvancedCrawlerConfig(
            enable_user_agent_rotation=True,
            enable_anti_detection=True,
            enable_session_management=True,
        )

        crawler = AdvancedCrawler(config)

        try:
            await crawler.setup()
            print("✓ 高级爬虫初始化成功")

            # 测试统计信息
            stats = crawler.stats
            print(f"✓ 统计信息获取成功: {stats}")

            await crawler.cleanup()
            print("✓ 高级爬虫清理成功")

        except Exception as e:
            print(f"✗ 测试失败: {e}")

    asyncio.run(run_basic_test())
