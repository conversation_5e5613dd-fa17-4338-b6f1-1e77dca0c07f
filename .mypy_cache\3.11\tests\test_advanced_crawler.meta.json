{"data_mtime": 1752423502, "dep_lines": [21, 25, 26, 27, 28, 29, 30, 175, 17, 8, 9, 10, 11, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 19], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 20, 5, 10, 10, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10], "dependencies": ["src.data_trans.crawlers.advanced_crawler", "src.data_trans.crawlers.anti_detection", "src.data_trans.crawlers.captcha_solver", "src.data_trans.crawlers.js_renderer", "src.data_trans.crawlers.proxy_pool", "src.data_trans.crawlers.session_manager", "src.data_trans.crawlers.user_agent_manager", "src.data_trans.crawlers.base_crawler", "unittest.mock", "asyncio", "os", "sys", "pathlib", "builtins", "_frozen_importlib", "abc", "datetime", "pydantic", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main", "src", "typing", "typing_extensions", "unittest"], "hash": "4f86764cbfaa19e5a0e4cb17ec1ee536c9ce9c06", "id": "tests.test_advanced_crawler", "ignore_all": false, "interface_hash": "6b19a91c6599b5f245c4b462c9445e0e2a5143f5", "mtime": 1752423500, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": true, "disable_error_code": [], "disable_memoryview_promotion": true, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": true, "disallow_untyped_calls": true, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": true, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": true, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": false, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": ["pydantic.mypy"], "strict_bytes": true, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": false, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "tests\\test_advanced_crawler.py", "plugin_data": [{"debug_dataclass_transform": false, "init_forbid_extra": true, "init_typed": true, "warn_required_dynamic_aliases": true}, null], "size": 13208, "suppressed": ["pytest"], "version_id": "1.16.1"}