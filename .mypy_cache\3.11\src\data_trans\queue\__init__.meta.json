{"data_mtime": 1752386759, "dep_lines": [7, 8, 9, 10, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 30, 30, 30], "dependencies": ["src.data_trans.queue.consumer_group", "src.data_trans.queue.queue_manager", "src.data_trans.queue.task_queue", "src.data_trans.queue.task_status", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "2e89481c511e3afd94227bc8f0b9540191de5f02", "id": "src.data_trans.queue", "ignore_all": false, "interface_hash": "0fcd123ef349d61715d4a9e1e22ee6f9f378449b", "mtime": 1752386757, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": true, "disable_error_code": [], "disable_memoryview_promotion": true, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": true, "disallow_untyped_calls": true, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": true, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": true, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": false, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": ["pydantic.mypy"], "strict_bytes": true, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": false, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "D:\\github_projects\\data_trans\\src\\data_trans\\queue\\__init__.py", "plugin_data": [{"debug_dataclass_transform": false, "init_forbid_extra": true, "init_typed": true, "warn_required_dynamic_aliases": true}, null], "size": 408, "suppressed": [], "version_id": "1.16.1"}