{".class": "MypyFile", "_fullname": "multiprocessing.shared_memory", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Generic": {".class": "SymbolTableNode", "cross_ref": "typing.Generic", "kind": "Gdef", "module_hidden": true, "module_public": false}, "GenericAlias": {".class": "SymbolTableNode", "cross_ref": "types.GenericAlias", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Self": {".class": "SymbolTableNode", "cross_ref": "typing.Self", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ShareableList": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "multiprocessing.shared_memory.ShareableList", "name": "ShareableList", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.shared_memory._SLT", "id": 1, "name": "_SLT", "namespace": "multiprocessing.shared_memory.ShareableList", "upper_bound": "builtins.object", "values": ["builtins.int", "builtins.float", "builtins.bool", "builtins.str", "builtins.bytes", {".class": "NoneType"}], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "multiprocessing.shared_memory.ShareableList", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "multiprocessing.shared_memory", "mro": ["multiprocessing.shared_memory.ShareableList", "builtins.object"], "names": {".class": "SymbolTable", "__class_getitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_trivial_self"], "fullname": "multiprocessing.shared_memory.ShareableList.__class_getitem__", "name": "__class_getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.shared_memory._SLT", "id": 1, "name": "_SLT", "namespace": "multiprocessing.shared_memory.ShareableList", "upper_bound": "builtins.object", "values": ["builtins.int", "builtins.float", "builtins.bool", "builtins.str", "builtins.bytes", {".class": "NoneType"}], "variance": 0}], "extra_attrs": null, "type_ref": "multiprocessing.shared_memory.ShareableList"}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__class_getitem__ of ShareableList", "ret_type": "types.GenericAlias", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__getitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "multiprocessing.shared_memory.ShareableList.__getitem__", "name": "__getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.shared_memory._SLT", "id": 1, "name": "_SLT", "namespace": "multiprocessing.shared_memory.ShareableList", "upper_bound": "builtins.object", "values": ["builtins.int", "builtins.float", "builtins.bool", "builtins.str", "builtins.bytes", {".class": "NoneType"}], "variance": 0}], "extra_attrs": null, "type_ref": "multiprocessing.shared_memory.ShareableList"}, "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__getitem__ of ShareableList", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.shared_memory._SLT", "id": 1, "name": "_SLT", "namespace": "multiprocessing.shared_memory.ShareableList", "upper_bound": "builtins.object", "values": ["builtins.int", "builtins.float", "builtins.bool", "builtins.str", "builtins.bytes", {".class": "NoneType"}], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "multiprocessing.shared_memory.ShareableList.__init__", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 5], "arg_names": ["self", "sequence", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated", "is_trivial_self"], "fullname": "multiprocessing.shared_memory.ShareableList.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 5], "arg_names": ["self", "sequence", "name"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.shared_memory._SLT", "id": 1, "name": "_SLT", "namespace": "multiprocessing.shared_memory.ShareableList", "upper_bound": "builtins.object", "values": ["builtins.int", "builtins.float", "builtins.bool", "builtins.str", "builtins.bytes", {".class": "NoneType"}], "variance": 0}], "extra_attrs": null, "type_ref": "multiprocessing.shared_memory.ShareableList"}, {".class": "NoneType"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of ShareableList", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "multiprocessing.shared_memory.ShareableList.__init__", "name": "__init__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 1, 5], "arg_names": ["self", "sequence", "name"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.shared_memory._SLT", "id": 1, "name": "_SLT", "namespace": "multiprocessing.shared_memory.ShareableList", "upper_bound": "builtins.object", "values": ["builtins.int", "builtins.float", "builtins.bool", "builtins.str", "builtins.bytes", {".class": "NoneType"}], "variance": 0}], "extra_attrs": null, "type_ref": "multiprocessing.shared_memory.ShareableList"}, {".class": "NoneType"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of ShareableList", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5], "arg_names": ["self", "sequence", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated", "is_trivial_self"], "fullname": "multiprocessing.shared_memory.ShareableList.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": ["self", "sequence", "name"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.shared_memory._SLT", "id": 1, "name": "_SLT", "namespace": "multiprocessing.shared_memory.ShareableList", "upper_bound": "builtins.object", "values": ["builtins.int", "builtins.float", "builtins.bool", "builtins.str", "builtins.bytes", {".class": "NoneType"}], "variance": 0}], "extra_attrs": null, "type_ref": "multiprocessing.shared_memory.ShareableList"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.shared_memory._SLT", "id": 1, "name": "_SLT", "namespace": "multiprocessing.shared_memory.ShareableList", "upper_bound": "builtins.object", "values": ["builtins.int", "builtins.float", "builtins.bool", "builtins.str", "builtins.bytes", {".class": "NoneType"}], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of ShareableList", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "multiprocessing.shared_memory.ShareableList.__init__", "name": "__init__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": ["self", "sequence", "name"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.shared_memory._SLT", "id": 1, "name": "_SLT", "namespace": "multiprocessing.shared_memory.ShareableList", "upper_bound": "builtins.object", "values": ["builtins.int", "builtins.float", "builtins.bool", "builtins.str", "builtins.bytes", {".class": "NoneType"}], "variance": 0}], "extra_attrs": null, "type_ref": "multiprocessing.shared_memory.ShareableList"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.shared_memory._SLT", "id": 1, "name": "_SLT", "namespace": "multiprocessing.shared_memory.ShareableList", "upper_bound": "builtins.object", "values": ["builtins.int", "builtins.float", "builtins.bool", "builtins.str", "builtins.bytes", {".class": "NoneType"}], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of ShareableList", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "setter_index": null, "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 1, 5], "arg_names": ["self", "sequence", "name"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.shared_memory._SLT", "id": 1, "name": "_SLT", "namespace": "multiprocessing.shared_memory.ShareableList", "upper_bound": "builtins.object", "values": ["builtins.int", "builtins.float", "builtins.bool", "builtins.str", "builtins.bytes", {".class": "NoneType"}], "variance": 0}], "extra_attrs": null, "type_ref": "multiprocessing.shared_memory.ShareableList"}, {".class": "NoneType"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of ShareableList", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": ["self", "sequence", "name"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.shared_memory._SLT", "id": 1, "name": "_SLT", "namespace": "multiprocessing.shared_memory.ShareableList", "upper_bound": "builtins.object", "values": ["builtins.int", "builtins.float", "builtins.bool", "builtins.str", "builtins.bytes", {".class": "NoneType"}], "variance": 0}], "extra_attrs": null, "type_ref": "multiprocessing.shared_memory.ShareableList"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.shared_memory._SLT", "id": 1, "name": "_SLT", "namespace": "multiprocessing.shared_memory.ShareableList", "upper_bound": "builtins.object", "values": ["builtins.int", "builtins.float", "builtins.bool", "builtins.str", "builtins.bytes", {".class": "NoneType"}], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of ShareableList", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "__len__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "multiprocessing.shared_memory.ShareableList.__len__", "name": "__len__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.shared_memory._SLT", "id": 1, "name": "_SLT", "namespace": "multiprocessing.shared_memory.ShareableList", "upper_bound": "builtins.object", "values": ["builtins.int", "builtins.float", "builtins.bool", "builtins.str", "builtins.bytes", {".class": "NoneType"}], "variance": 0}], "extra_attrs": null, "type_ref": "multiprocessing.shared_memory.ShareableList"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__len__ of ShareableList", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__reduce__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "multiprocessing.shared_memory.ShareableList.__reduce__", "name": "__reduce__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.shared_memory.ShareableList.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.shared_memory._SLT", "id": 1, "name": "_SLT", "namespace": "multiprocessing.shared_memory.ShareableList", "upper_bound": "builtins.object", "values": ["builtins.int", "builtins.float", "builtins.bool", "builtins.str", "builtins.bytes", {".class": "NoneType"}], "variance": 0}], "extra_attrs": null, "type_ref": "multiprocessing.shared_memory.ShareableList"}, "values": [], "variance": 0}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__reduce__ of ShareableList", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.shared_memory.ShareableList.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.shared_memory._SLT", "id": 1, "name": "_SLT", "namespace": "multiprocessing.shared_memory.ShareableList", "upper_bound": "builtins.object", "values": ["builtins.int", "builtins.float", "builtins.bool", "builtins.str", "builtins.bytes", {".class": "NoneType"}], "variance": 0}], "extra_attrs": null, "type_ref": "multiprocessing.shared_memory.ShareableList"}, "values": [], "variance": 0}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.shared_memory._SLT", "id": 1, "name": "_SLT", "namespace": "multiprocessing.shared_memory.ShareableList", "upper_bound": "builtins.object", "values": ["builtins.int", "builtins.float", "builtins.bool", "builtins.str", "builtins.bytes", {".class": "NoneType"}], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.shared_memory.ShareableList.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.shared_memory._SLT", "id": 1, "name": "_SLT", "namespace": "multiprocessing.shared_memory.ShareableList", "upper_bound": "builtins.object", "values": ["builtins.int", "builtins.float", "builtins.bool", "builtins.str", "builtins.bytes", {".class": "NoneType"}], "variance": 0}], "extra_attrs": null, "type_ref": "multiprocessing.shared_memory.ShareableList"}, "values": [], "variance": 0}]}}}, "__setitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "multiprocessing.shared_memory.ShareableList.__setitem__", "name": "__setitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.shared_memory._SLT", "id": 1, "name": "_SLT", "namespace": "multiprocessing.shared_memory.ShareableList", "upper_bound": "builtins.object", "values": ["builtins.int", "builtins.float", "builtins.bool", "builtins.str", "builtins.bytes", {".class": "NoneType"}], "variance": 0}], "extra_attrs": null, "type_ref": "multiprocessing.shared_memory.ShareableList"}, "builtins.int", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.shared_memory._SLT", "id": 1, "name": "_SLT", "namespace": "multiprocessing.shared_memory.ShareableList", "upper_bound": "builtins.object", "values": ["builtins.int", "builtins.float", "builtins.bool", "builtins.str", "builtins.bytes", {".class": "NoneType"}], "variance": 0}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__setitem__ of ShareableList", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "count": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "multiprocessing.shared_memory.ShareableList.count", "name": "count", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "value"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.shared_memory._SLT", "id": 1, "name": "_SLT", "namespace": "multiprocessing.shared_memory.ShareableList", "upper_bound": "builtins.object", "values": ["builtins.int", "builtins.float", "builtins.bool", "builtins.str", "builtins.bytes", {".class": "NoneType"}], "variance": 0}], "extra_attrs": null, "type_ref": "multiprocessing.shared_memory.ShareableList"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.shared_memory._SLT", "id": 1, "name": "_SLT", "namespace": "multiprocessing.shared_memory.ShareableList", "upper_bound": "builtins.object", "values": ["builtins.int", "builtins.float", "builtins.bool", "builtins.str", "builtins.bytes", {".class": "NoneType"}], "variance": 0}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "count of ShareableList", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "format": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "multiprocessing.shared_memory.ShareableList.format", "name": "format", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.shared_memory._SLT", "id": 1, "name": "_SLT", "namespace": "multiprocessing.shared_memory.ShareableList", "upper_bound": "builtins.object", "values": ["builtins.int", "builtins.float", "builtins.bool", "builtins.str", "builtins.bytes", {".class": "NoneType"}], "variance": 0}], "extra_attrs": null, "type_ref": "multiprocessing.shared_memory.ShareableList"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "format of ShareableList", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "multiprocessing.shared_memory.ShareableList.format", "name": "format", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.shared_memory._SLT", "id": 1, "name": "_SLT", "namespace": "multiprocessing.shared_memory.ShareableList", "upper_bound": "builtins.object", "values": ["builtins.int", "builtins.float", "builtins.bool", "builtins.str", "builtins.bytes", {".class": "NoneType"}], "variance": 0}], "extra_attrs": null, "type_ref": "multiprocessing.shared_memory.ShareableList"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "format of ShareableList", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "index": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "multiprocessing.shared_memory.ShareableList.index", "name": "index", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "value"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.shared_memory._SLT", "id": 1, "name": "_SLT", "namespace": "multiprocessing.shared_memory.ShareableList", "upper_bound": "builtins.object", "values": ["builtins.int", "builtins.float", "builtins.bool", "builtins.str", "builtins.bytes", {".class": "NoneType"}], "variance": 0}], "extra_attrs": null, "type_ref": "multiprocessing.shared_memory.ShareableList"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.shared_memory._SLT", "id": 1, "name": "_SLT", "namespace": "multiprocessing.shared_memory.ShareableList", "upper_bound": "builtins.object", "values": ["builtins.int", "builtins.float", "builtins.bool", "builtins.str", "builtins.bytes", {".class": "NoneType"}], "variance": 0}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "index of ShareableList", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "shm": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "multiprocessing.shared_memory.ShareableList.shm", "name": "shm", "setter_type": null, "type": "multiprocessing.shared_memory.SharedMemory"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.shared_memory.ShareableList.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.shared_memory._SLT", "id": 1, "name": "_SLT", "namespace": "multiprocessing.shared_memory.ShareableList", "upper_bound": "builtins.object", "values": ["builtins.int", "builtins.float", "builtins.bool", "builtins.str", "builtins.bytes", {".class": "NoneType"}], "variance": 0}], "extra_attrs": null, "type_ref": "multiprocessing.shared_memory.ShareableList"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_SLT"], "typeddict_type": null}}, "SharedMemory": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "multiprocessing.shared_memory.SharedMemory", "name": "SharedMemory", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "multiprocessing.shared_memory.SharedMemory", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "multiprocessing.shared_memory", "mro": ["multiprocessing.shared_memory.SharedMemory", "builtins.object"], "names": {".class": "SymbolTable", "__del__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "multiprocessing.shared_memory.SharedMemory.__del__", "name": "__del__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["multiprocessing.shared_memory.SharedMemory"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__del__ of SharedMemory", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "name", "create", "size"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "multiprocessing.shared_memory.SharedMemory.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "name", "create", "size"], "arg_types": ["multiprocessing.shared_memory.SharedMemory", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of SharedMemory", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "buf": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "multiprocessing.shared_memory.SharedMemory.buf", "name": "buf", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["multiprocessing.shared_memory.SharedMemory"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "buf of SharedMemory", "ret_type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.memoryview"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "multiprocessing.shared_memory.SharedMemory.buf", "name": "buf", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["multiprocessing.shared_memory.SharedMemory"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "buf of SharedMemory", "ret_type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.memoryview"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "multiprocessing.shared_memory.SharedMemory.close", "name": "close", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["multiprocessing.shared_memory.SharedMemory"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "close of SharedMemory", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "multiprocessing.shared_memory.SharedMemory.name", "name": "name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["multiprocessing.shared_memory.SharedMemory"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "name of SharedMemory", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "multiprocessing.shared_memory.SharedMemory.name", "name": "name", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["multiprocessing.shared_memory.SharedMemory"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "name of SharedMemory", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "multiprocessing.shared_memory.SharedMemory.size", "name": "size", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["multiprocessing.shared_memory.SharedMemory"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "size of SharedMemory", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "multiprocessing.shared_memory.SharedMemory.size", "name": "size", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["multiprocessing.shared_memory.SharedMemory"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "size of SharedMemory", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "unlink": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "multiprocessing.shared_memory.SharedMemory.unlink", "name": "unlink", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["multiprocessing.shared_memory.SharedMemory"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "unlink of SharedMemory", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.shared_memory.SharedMemory.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "multiprocessing.shared_memory.SharedMemory", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_SLT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "multiprocessing.shared_memory._SLT", "name": "_SLT", "upper_bound": "builtins.object", "values": ["builtins.int", "builtins.float", "builtins.bool", "builtins.str", "builtins.bytes", {".class": "NoneType"}], "variance": 0}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "multiprocessing.shared_memory.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "multiprocessing.shared_memory.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "multiprocessing.shared_memory.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "multiprocessing.shared_memory.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "multiprocessing.shared_memory.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "multiprocessing.shared_memory.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "multiprocessing.shared_memory.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef", "module_hidden": true, "module_public": false}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "C:\\Users\\<USER>\\.cache\\pre-commit\\repoyp9f_v0e\\py_env-python3.11\\Lib\\site-packages\\mypy\\typeshed\\stdlib\\multiprocessing\\shared_memory.pyi"}