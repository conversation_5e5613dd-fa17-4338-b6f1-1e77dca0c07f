{".class": "MypyFile", "_fullname": "src.data_trans.crawlers.anti_detection", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ActionChains": {".class": "SymbolTableNode", "kind": "Gdef", "module_hidden": true, "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "src.data_trans.crawlers.anti_detection.ActionChains", "name": "ActionChains", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "src.data_trans.crawlers.anti_detection.ActionChains", "source_any": null, "type_of_any": 3}}}, "AntiDetection": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "src.data_trans.crawlers.anti_detection.AntiDetection", "name": "AntiDetection", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "src.data_trans.crawlers.anti_detection.AntiDetection", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "src.data_trans.crawlers.anti_detection", "mro": ["src.data_trans.crawlers.anti_detection.AntiDetection", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "src.data_trans.crawlers.anti_detection.AntiDetection.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "config"], "arg_types": ["src.data_trans.crawlers.anti_detection.AntiDetection", "src.data_trans.crawlers.anti_detection.AntiDetectionConfig"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of AntiDetection", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_randomized_headers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "src.data_trans.crawlers.anti_detection.AntiDetection._get_randomized_headers", "name": "_get_randomized_headers", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["src.data_trans.crawlers.anti_detection.AntiDetection"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_randomized_headers of AntiDetection", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_header_templates": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "src.data_trans.crawlers.anti_detection.AntiDetection._header_templates", "name": "_header_templates", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_last_request_time": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "src.data_trans.crawlers.anti_detection.AntiDetection._last_request_time", "name": "_last_request_time", "setter_type": null, "type": "builtins.float"}}, "_request_count": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "src.data_trans.crawlers.anti_detection.AntiDetection._request_count", "name": "_request_count", "setter_type": null, "type": "builtins.int"}}, "_session_start_time": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "src.data_trans.crawlers.anti_detection.AntiDetection._session_start_time", "name": "_session_start_time", "setter_type": null, "type": "builtins.float"}}, "_simulate_mouse_movement": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "driver", "actions"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "src.data_trans.crawlers.anti_detection.AntiDetection._simulate_mouse_movement", "name": "_simulate_mouse_movement", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "driver", "actions"], "arg_types": ["src.data_trans.crawlers.anti_detection.AntiDetection", {".class": "AnyType", "missing_import_name": "src.data_trans.crawlers.anti_detection.webdriver", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "src.data_trans.crawlers.anti_detection.ActionChains", "source_any": null, "type_of_any": 3}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_simulate_mouse_movement of AntiDetection", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_simulate_scrolling": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "driver"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "src.data_trans.crawlers.anti_detection.AntiDetection._simulate_scrolling", "name": "_simulate_scrolling", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "driver"], "arg_types": ["src.data_trans.crawlers.anti_detection.AntiDetection", {".class": "AnyType", "missing_import_name": "src.data_trans.crawlers.anti_detection.webdriver", "source_any": null, "type_of_any": 3}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_simulate_scrolling of AntiDetection", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_ua_manager": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "src.data_trans.crawlers.anti_detection.AntiDetection._ua_manager", "name": "_ua_manager", "setter_type": null, "type": "src.data_trans.crawlers.user_agent_manager.UserAgentManager"}}, "apply_delay": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "src.data_trans.crawlers.anti_detection.AntiDetection.apply_delay", "name": "apply_delay", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["src.data_trans.crawlers.anti_detection.AntiDetection"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "apply_delay of AntiDetection", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "src.data_trans.crawlers.anti_detection.AntiDetection.config", "name": "config", "setter_type": null, "type": "src.data_trans.crawlers.anti_detection.AntiDetectionConfig"}}, "create_stealth_driver": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "headless"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "src.data_trans.crawlers.anti_detection.AntiDetection.create_stealth_driver", "name": "create_stealth_driver", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "headless"], "arg_types": ["src.data_trans.crawlers.anti_detection.AntiDetection", "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_stealth_driver of AntiDetection", "ret_type": {".class": "AnyType", "missing_import_name": "src.data_trans.crawlers.anti_detection.webdriver", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_random_headers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "base_headers"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "src.data_trans.crawlers.anti_detection.AntiDetection.get_random_headers", "name": "get_random_headers", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "base_headers"], "arg_types": ["src.data_trans.crawlers.anti_detection.AntiDetection", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_random_headers of AntiDetection", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_retry_delay": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "attempt"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "src.data_trans.crawlers.anti_detection.AntiDetection.get_retry_delay", "name": "get_retry_delay", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "attempt"], "arg_types": ["src.data_trans.crawlers.anti_detection.AntiDetection", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_retry_delay of AntiDetection", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "reset_session": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "src.data_trans.crawlers.anti_detection.AntiDetection.reset_session", "name": "reset_session", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["src.data_trans.crawlers.anti_detection.AntiDetection"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "reset_session of AntiDetection", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "should_retry": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "status_code", "response_content"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "src.data_trans.crawlers.anti_detection.AntiDetection.should_retry", "name": "should_retry", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "status_code", "response_content"], "arg_types": ["src.data_trans.crawlers.anti_detection.AntiDetection", "builtins.int", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "should_retry of AntiDetection", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "should_rotate_session": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "src.data_trans.crawlers.anti_detection.AntiDetection.should_rotate_session", "name": "should_rotate_session", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["src.data_trans.crawlers.anti_detection.AntiDetection"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "should_rotate_session of AntiDetection", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "simulate_human_behavior": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "driver", "page_url"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "src.data_trans.crawlers.anti_detection.AntiDetection.simulate_human_behavior", "name": "simulate_human_behavior", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "driver", "page_url"], "arg_types": ["src.data_trans.crawlers.anti_detection.AntiDetection", {".class": "AnyType", "missing_import_name": "src.data_trans.crawlers.anti_detection.webdriver", "source_any": null, "type_of_any": 3}, "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "simulate_human_behavior of AntiDetection", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "stats": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "src.data_trans.crawlers.anti_detection.AntiDetection.stats", "name": "stats", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["src.data_trans.crawlers.anti_detection.AntiDetection"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "stats of AntiDetection", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "src.data_trans.crawlers.anti_detection.AntiDetection.stats", "name": "stats", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["src.data_trans.crawlers.anti_detection.AntiDetection"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "stats of AntiDetection", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "src.data_trans.crawlers.anti_detection.AntiDetection.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "src.data_trans.crawlers.anti_detection.AntiDetection", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AntiDetectionConfig": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pydantic.main.BaseModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "src.data_trans.crawlers.anti_detection.AntiDetectionConfig", "name": "AntiDetectionConfig", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "src.data_trans.crawlers.anti_detection.AntiDetectionConfig", "has_param_spec_type": false, "metaclass_type": "pydantic._internal._model_construction.ModelMetaclass", "metadata": {"pydantic-mypy-metadata": {"class_vars": {}, "config": {}, "fields": {"burst_requests_enabled": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 62, "name": "burst_requests_enabled", "strict": null, "type": "builtins.bool"}, "enable_canvas_fingerprint_protection": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 74, "name": "enable_canvas_fingerprint_protection", "strict": null, "type": "builtins.bool"}, "enable_fingerprint_randomization": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 50, "name": "enable_fingerprint_randomization", "strict": null, "type": "builtins.bool"}, "enable_header_rotation": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 35, "name": "enable_header_rotation", "strict": null, "type": "builtins.bool"}, "enable_keyboard_simulation": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 42, "name": "enable_keyboard_simulation", "strict": null, "type": "builtins.bool"}, "enable_mouse_simulation": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 41, "name": "enable_mouse_simulation", "strict": null, "type": "builtins.bool"}, "enable_random_delay": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 30, "name": "enable_random_delay", "strict": null, "type": "builtins.bool"}, "enable_request_pattern_randomization": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 59, "name": "enable_request_pattern_randomization", "strict": null, "type": "builtins.bool"}, "enable_scroll_simulation": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 45, "name": "enable_scroll_simulation", "strict": null, "type": "builtins.bool"}, "enable_session_rotation": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 55, "name": "enable_session_rotation", "strict": null, "type": "builtins.bool"}, "enable_smart_retry": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 65, "name": "enable_smart_retry", "strict": null, "type": "builtins.bool"}, "enable_user_agent_rotation": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 36, "name": "enable_user_agent_rotation", "strict": null, "type": "builtins.bool"}, "enable_webdriver_stealth": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 71, "name": "enable_webdriver_stealth", "strict": null, "type": "builtins.bool"}, "max_delay": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 32, "name": "max_delay", "strict": null, "type": "builtins.float"}, "min_delay": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 31, "name": "min_delay", "strict": null, "type": "builtins.float"}, "retry_on_status_codes": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 66, "name": "retry_on_status_codes", "strict": null, "type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}}, "session_lifetime": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 56, "name": "session_lifetime", "strict": null, "type": "builtins.float"}}}}, "module_name": "src.data_trans.crawlers.anti_detection", "mro": ["src.data_trans.crawlers.anti_detection.AntiDetectionConfig", "pydantic.main.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["__pydantic_self__", "enable_random_delay", "min_delay", "max_delay", "enable_header_rotation", "enable_user_agent_rotation", "enable_mouse_simulation", "enable_keyboard_simulation", "enable_scroll_simulation", "enable_fingerprint_randomization", "enable_session_rotation", "session_lifetime", "enable_request_pattern_randomization", "burst_requests_enabled", "enable_smart_retry", "retry_on_status_codes", "enable_webdriver_stealth", "enable_canvas_fingerprint_protection"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.data_trans.crawlers.anti_detection.AntiDetectionConfig.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["__pydantic_self__", "enable_random_delay", "min_delay", "max_delay", "enable_header_rotation", "enable_user_agent_rotation", "enable_mouse_simulation", "enable_keyboard_simulation", "enable_scroll_simulation", "enable_fingerprint_randomization", "enable_session_rotation", "session_lifetime", "enable_request_pattern_randomization", "burst_requests_enabled", "enable_smart_retry", "retry_on_status_codes", "enable_webdriver_stealth", "enable_canvas_fingerprint_protection"], "arg_types": ["src.data_trans.crawlers.anti_detection.AntiDetectionConfig", "builtins.bool", "builtins.float", "builtins.float", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.float", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.bool", "builtins.bool"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of AntiDetectionConfig", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "burst_requests_enabled": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.crawlers.anti_detection.AntiDetectionConfig.burst_requests_enabled", "name": "burst_requests_enabled", "setter_type": null, "type": "builtins.bool"}}, "enable_canvas_fingerprint_protection": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.crawlers.anti_detection.AntiDetectionConfig.enable_canvas_fingerprint_protection", "name": "enable_canvas_fingerprint_protection", "setter_type": null, "type": "builtins.bool"}}, "enable_fingerprint_randomization": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.crawlers.anti_detection.AntiDetectionConfig.enable_fingerprint_randomization", "name": "enable_fingerprint_randomization", "setter_type": null, "type": "builtins.bool"}}, "enable_header_rotation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.crawlers.anti_detection.AntiDetectionConfig.enable_header_rotation", "name": "enable_header_rotation", "setter_type": null, "type": "builtins.bool"}}, "enable_keyboard_simulation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.crawlers.anti_detection.AntiDetectionConfig.enable_keyboard_simulation", "name": "enable_keyboard_simulation", "setter_type": null, "type": "builtins.bool"}}, "enable_mouse_simulation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.crawlers.anti_detection.AntiDetectionConfig.enable_mouse_simulation", "name": "enable_mouse_simulation", "setter_type": null, "type": "builtins.bool"}}, "enable_random_delay": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.crawlers.anti_detection.AntiDetectionConfig.enable_random_delay", "name": "enable_random_delay", "setter_type": null, "type": "builtins.bool"}}, "enable_request_pattern_randomization": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.crawlers.anti_detection.AntiDetectionConfig.enable_request_pattern_randomization", "name": "enable_request_pattern_randomization", "setter_type": null, "type": "builtins.bool"}}, "enable_scroll_simulation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.crawlers.anti_detection.AntiDetectionConfig.enable_scroll_simulation", "name": "enable_scroll_simulation", "setter_type": null, "type": "builtins.bool"}}, "enable_session_rotation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.crawlers.anti_detection.AntiDetectionConfig.enable_session_rotation", "name": "enable_session_rotation", "setter_type": null, "type": "builtins.bool"}}, "enable_smart_retry": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.crawlers.anti_detection.AntiDetectionConfig.enable_smart_retry", "name": "enable_smart_retry", "setter_type": null, "type": "builtins.bool"}}, "enable_user_agent_rotation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.crawlers.anti_detection.AntiDetectionConfig.enable_user_agent_rotation", "name": "enable_user_agent_rotation", "setter_type": null, "type": "builtins.bool"}}, "enable_webdriver_stealth": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.crawlers.anti_detection.AntiDetectionConfig.enable_webdriver_stealth", "name": "enable_webdriver_stealth", "setter_type": null, "type": "builtins.bool"}}, "max_delay": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.crawlers.anti_detection.AntiDetectionConfig.max_delay", "name": "max_delay", "setter_type": null, "type": "builtins.float"}}, "min_delay": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.crawlers.anti_detection.AntiDetectionConfig.min_delay", "name": "min_delay", "setter_type": null, "type": "builtins.float"}}, "model_construct": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": [null, "_fields_set", "enable_random_delay", "min_delay", "max_delay", "enable_header_rotation", "enable_user_agent_rotation", "enable_mouse_simulation", "enable_keyboard_simulation", "enable_scroll_simulation", "enable_fingerprint_randomization", "enable_session_rotation", "session_lifetime", "enable_request_pattern_randomization", "burst_requests_enabled", "enable_smart_retry", "retry_on_status_codes", "enable_webdriver_stealth", "enable_canvas_fingerprint_protection"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "src.data_trans.crawlers.anti_detection.AntiDetectionConfig.model_construct", "name": "model_construct", "type": {".class": "CallableType", "arg_kinds": [0, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["_cls", "_fields_set", "enable_random_delay", "min_delay", "max_delay", "enable_header_rotation", "enable_user_agent_rotation", "enable_mouse_simulation", "enable_keyboard_simulation", "enable_scroll_simulation", "enable_fingerprint_randomization", "enable_session_rotation", "session_lifetime", "enable_request_pattern_randomization", "burst_requests_enabled", "enable_smart_retry", "retry_on_status_codes", "enable_webdriver_stealth", "enable_canvas_fingerprint_protection"], "arg_types": [{".class": "TypeType", "item": "src.data_trans.crawlers.anti_detection.AntiDetectionConfig"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.float", "builtins.float", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.float", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.bool", "builtins.bool"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "model_construct of AntiDetectionConfig", "ret_type": "src.data_trans.crawlers.anti_detection.AntiDetectionConfig", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready"], "fullname": "src.data_trans.crawlers.anti_detection.AntiDetectionConfig.model_construct", "name": "model_construct", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["_cls", "_fields_set", "enable_random_delay", "min_delay", "max_delay", "enable_header_rotation", "enable_user_agent_rotation", "enable_mouse_simulation", "enable_keyboard_simulation", "enable_scroll_simulation", "enable_fingerprint_randomization", "enable_session_rotation", "session_lifetime", "enable_request_pattern_randomization", "burst_requests_enabled", "enable_smart_retry", "retry_on_status_codes", "enable_webdriver_stealth", "enable_canvas_fingerprint_protection"], "arg_types": [{".class": "TypeType", "item": "src.data_trans.crawlers.anti_detection.AntiDetectionConfig"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.float", "builtins.float", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.float", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.bool", "builtins.bool"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "model_construct of AntiDetectionConfig", "ret_type": "src.data_trans.crawlers.anti_detection.AntiDetectionConfig", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "retry_on_status_codes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.crawlers.anti_detection.AntiDetectionConfig.retry_on_status_codes", "name": "retry_on_status_codes", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "session_lifetime": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.crawlers.anti_detection.AntiDetectionConfig.session_lifetime", "name": "session_lifetime", "setter_type": null, "type": "builtins.float"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "src.data_trans.crawlers.anti_detection.AntiDetectionConfig.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "src.data_trans.crawlers.anti_detection.AntiDetectionConfig", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BaseModel": {".class": "SymbolTableNode", "cross_ref": "pydantic.main.BaseModel", "kind": "Gdef", "module_hidden": true, "module_public": false}, "By": {".class": "SymbolTableNode", "kind": "Gdef", "module_hidden": true, "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "src.data_trans.crawlers.anti_detection.By", "name": "By", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "src.data_trans.crawlers.anti_detection.By", "source_any": null, "type_of_any": 3}}}, "ChromeOptions": {".class": "SymbolTableNode", "kind": "Gdef", "module_hidden": true, "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "src.data_trans.crawlers.anti_detection.ChromeOptions", "name": "ChromeOptions", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "src.data_trans.crawlers.anti_detection.ChromeOptions", "source_any": null, "type_of_any": 3}}}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Field": {".class": "SymbolTableNode", "cross_ref": "pydantic.fields.Field", "kind": "Gdef", "module_hidden": true, "module_public": false}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef", "module_hidden": true, "module_public": false}, "UserAgentConfig": {".class": "SymbolTableNode", "cross_ref": "src.data_trans.crawlers.user_agent_manager.UserAgentConfig", "kind": "Gdef", "module_hidden": true, "module_public": false}, "UserAgentManager": {".class": "SymbolTableNode", "cross_ref": "src.data_trans.crawlers.user_agent_manager.UserAgentManager", "kind": "Gdef", "module_hidden": true, "module_public": false}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.crawlers.anti_detection.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.crawlers.anti_detection.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.crawlers.anti_detection.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.crawlers.anti_detection.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.crawlers.anti_detection.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.crawlers.anti_detection.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "asyncio": {".class": "SymbolTableNode", "cross_ref": "asyncio", "kind": "Gdef", "module_hidden": true, "module_public": false}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "src.data_trans.crawlers.anti_detection.logger", "name": "logger", "setter_type": null, "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef", "module_hidden": true, "module_public": false}, "random": {".class": "SymbolTableNode", "cross_ref": "random", "kind": "Gdef", "module_hidden": true, "module_public": false}, "stealth": {".class": "SymbolTableNode", "kind": "Gdef", "module_hidden": true, "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "src.data_trans.crawlers.anti_detection.stealth", "name": "stealth", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "src.data_trans.crawlers.anti_detection.stealth", "source_any": null, "type_of_any": 3}}}, "time": {".class": "SymbolTableNode", "cross_ref": "time", "kind": "Gdef", "module_hidden": true, "module_public": false}, "webdriver": {".class": "SymbolTableNode", "kind": "Gdef", "module_hidden": true, "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "src.data_trans.crawlers.anti_detection.webdriver", "name": "webdriver", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "src.data_trans.crawlers.anti_detection.webdriver", "source_any": null, "type_of_any": 3}}}}, "path": "src\\data_trans\\crawlers\\anti_detection.py"}