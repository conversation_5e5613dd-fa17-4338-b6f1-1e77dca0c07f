"""
高级爬虫功能简化测试

直接测试高级爬虫的各个组件，避免复杂的依赖问题。
"""

import asyncio
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


async def test_user_agent_manager():
    """测试User-Agent管理器"""
    print("测试User-Agent管理器...")

    try:
        from src.data_trans.crawlers.user_agent_manager import (
            UserAgentConfig,
            UserAgentManager,
        )

        config = UserAgentConfig()
        manager = UserAgentManager(config)

        # 测试获取随机User-Agent
        ua1 = manager.get_random_user_agent()
        ua2 = manager.get_random_user_agent("chrome")
        ua3 = manager.get_user_agent_by_rotation()

        print(f"  ✓ 随机User-Agent: {ua1[:50]}...")
        print(f"  ✓ Chrome User-Agent: {ua2[:50]}...")
        print(f"  ✓ 轮换User-Agent: {ua3[:50]}...")

        # 测试移动端User-Agent
        mobile_ua = manager.get_mobile_user_agent("android")
        print(f"  ✓ 移动端User-Agent: {mobile_ua[:50]}...")

        # 测试统计信息
        stats = manager.stats
        print(f"  ✓ 统计信息: {stats}")

        print("✓ User-Agent管理器测试通过")
        return True

    except Exception as e:
        print(f"✗ User-Agent管理器测试失败: {e}")
        return False


async def test_proxy_pool():
    """测试代理池"""
    print("测试代理池...")

    try:
        from src.data_trans.crawlers.proxy_pool import (
            ProxyInfo,
            ProxyPool,
            ProxyPoolConfig,
        )

        config = ProxyPoolConfig(
            max_proxies=5,
            enable_free_sources=False,  # 禁用免费源避免网络请求
            check_interval=60.0,
        )

        pool = ProxyPool(config)

        # 手动添加测试代理
        test_proxy = ProxyInfo(host="127.0.0.1", port=8080, protocol="http")

        pool._proxies["127.0.0.1:8080"] = test_proxy
        pool._working_proxies = ["127.0.0.1:8080"]

        # 测试获取代理
        proxy = await pool.get_proxy()
        print(f"  ✓ 获取代理: {proxy.url if proxy else 'None'}")

        # 测试随机代理
        random_proxy = await pool.get_random_proxy()
        print(f"  ✓ 随机代理: {random_proxy.url if random_proxy else 'None'}")

        # 测试统计信息
        stats = pool.stats
        print(f"  ✓ 统计信息: {stats}")

        print("✓ 代理池测试通过")
        return True

    except Exception as e:
        print(f"✗ 代理池测试失败: {e}")
        return False


async def test_anti_detection():
    """测试反检测功能"""
    print("测试反检测功能...")

    try:
        from src.data_trans.crawlers.anti_detection import (
            AntiDetection,
            AntiDetectionConfig,
        )

        config = AntiDetectionConfig(
            enable_random_delay=True,
            min_delay=0.1,
            max_delay=0.5,
            enable_header_rotation=True,
            enable_user_agent_rotation=True,
        )

        anti_detection = AntiDetection(config)

        # 测试随机请求头
        headers = anti_detection.get_random_headers()
        print(f"  ✓ 随机请求头数量: {len(headers)}")

        # 测试重试判断
        should_retry_403 = anti_detection.should_retry(403, "blocked")
        should_retry_200 = anti_detection.should_retry(200, "success")
        print(f"  ✓ 403状态码重试判断: {should_retry_403}")
        print(f"  ✓ 200状态码重试判断: {should_retry_200}")

        # 测试重试延迟
        delay = anti_detection.get_retry_delay(1)
        print(f"  ✓ 重试延迟: {delay:.2f}秒")

        # 测试会话轮换判断
        should_rotate = anti_detection.should_rotate_session()
        print(f"  ✓ 会话轮换判断: {should_rotate}")

        # 测试统计信息
        stats = anti_detection.stats
        print(f"  ✓ 统计信息: {stats}")

        print("✓ 反检测功能测试通过")
        return True

    except Exception as e:
        print(f"✗ 反检测功能测试失败: {e}")
        return False


async def test_session_manager():
    """测试会话管理器"""
    print("测试会话管理器...")

    try:
        from src.data_trans.crawlers.session_manager import (
            SessionConfig,
            SessionManager,
        )

        config = SessionConfig(
            max_sessions=3,
            session_timeout=60.0,
            enable_cookie_persistence=False,  # 禁用持久化避免文件操作
            enable_health_check=False,  # 禁用健康检查避免网络请求
        )

        manager = SessionManager(config)
        await manager.start()

        # 测试获取会话
        session_info = await manager.get_session()
        if session_info:
            session_id, client, semaphore = session_info
            print(f"  ✓ 获取会话: {session_id}")

            # 测试报告请求结果
            await manager.report_request_result(session_id, True)
            await manager.report_request_result(session_id, False)
            print("  ✓ 报告请求结果成功")

        # 测试统计信息
        stats = manager.stats
        print(f"  ✓ 统计信息: {stats}")

        await manager.stop()
        print("✓ 会话管理器测试通过")
        return True

    except Exception as e:
        print(f"✗ 会话管理器测试失败: {e}")
        return False


async def test_captcha_solver():
    """测试验证码识别器"""
    print("测试验证码识别器...")

    try:
        from src.data_trans.crawlers.captcha_solver import CaptchaConfig, CaptchaSolver

        config = CaptchaConfig(solver_type="2captcha", api_key="test_key", timeout=10.0)

        solver = CaptchaSolver(config)
        await solver.start()

        # 测试配置
        print(f"  ✓ 验证码服务类型: {config.solver_type}")
        print(f"  ✓ 支持的验证码类型: {config.supported_types}")

        await solver.stop()
        print("✓ 验证码识别器测试通过")
        return True

    except Exception as e:
        print(f"✗ 验证码识别器测试失败: {e}")
        return False


async def test_js_renderer_config():
    """测试JavaScript渲染器配置"""
    print("测试JavaScript渲染器配置...")

    try:
        from src.data_trans.crawlers.js_renderer import JSRendererConfig

        config = JSRendererConfig(
            renderer_type="playwright",
            headless=True,
            timeout=30.0,
            browser_type="chromium",
        )

        print(f"  ✓ 渲染器类型: {config.renderer_type}")
        print(f"  ✓ 无头模式: {config.headless}")
        print(f"  ✓ 浏览器类型: {config.browser_type}")
        print(f"  ✓ 超时时间: {config.timeout}")

        print("✓ JavaScript渲染器配置测试通过")
        return True

    except Exception as e:
        print(f"✗ JavaScript渲染器配置测试失败: {e}")
        return False


async def main():
    """运行所有测试"""
    print("开始高级爬虫功能测试...\n")

    tests = [
        test_user_agent_manager,
        test_proxy_pool,
        test_anti_detection,
        test_session_manager,
        test_captcha_solver,
        test_js_renderer_config,
    ]

    passed = 0
    total = len(tests)

    for test in tests:
        try:
            result = await test()
            if result:
                passed += 1
            print()
        except Exception as e:
            print(f"✗ 测试异常: {e}\n")

    print(f"测试完成: {passed}/{total} 通过")

    if passed == total:
        print("🎉 所有测试通过！")
    else:
        print("⚠️  部分测试失败")


if __name__ == "__main__":
    asyncio.run(main())
