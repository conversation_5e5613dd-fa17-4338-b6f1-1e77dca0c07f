{".class": "MypyFile", "_fullname": "tests.test_advanced_crawler", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AdvancedCrawler": {".class": "SymbolTableNode", "cross_ref": "src.data_trans.crawlers.advanced_crawler.AdvancedCrawler", "kind": "Gdef", "module_hidden": true, "module_public": false}, "AdvancedCrawlerConfig": {".class": "SymbolTableNode", "cross_ref": "src.data_trans.crawlers.advanced_crawler.AdvancedCrawlerConfig", "kind": "Gdef", "module_hidden": true, "module_public": false}, "AntiDetectionConfig": {".class": "SymbolTableNode", "cross_ref": "src.data_trans.crawlers.anti_detection.AntiDetectionConfig", "kind": "Gdef", "module_hidden": true, "module_public": false}, "AsyncMock": {".class": "SymbolTableNode", "cross_ref": "unittest.mock.AsyncMock", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CaptchaConfig": {".class": "SymbolTableNode", "cross_ref": "src.data_trans.crawlers.captcha_solver.CaptchaConfig", "kind": "Gdef", "module_hidden": true, "module_public": false}, "JSRendererConfig": {".class": "SymbolTableNode", "cross_ref": "src.data_trans.crawlers.js_renderer.JSRendererConfig", "kind": "Gdef", "module_hidden": true, "module_public": false}, "MagicMock": {".class": "SymbolTableNode", "cross_ref": "unittest.mock.MagicMock", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Path": {".class": "SymbolTableNode", "cross_ref": "pathlib.Path", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ProxyPoolConfig": {".class": "SymbolTableNode", "cross_ref": "src.data_trans.crawlers.proxy_pool.ProxyPoolConfig", "kind": "Gdef", "module_hidden": true, "module_public": false}, "SessionConfig": {".class": "SymbolTableNode", "cross_ref": "src.data_trans.crawlers.session_manager.SessionConfig", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TestAdvancedCrawler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tests.test_advanced_crawler.TestAdvancedCrawler", "name": "TestAdvancedCrawler", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "tests.test_advanced_crawler.TestAdvancedCrawler", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tests.test_advanced_crawler", "mro": ["tests.test_advanced_crawler.TestAdvancedCrawler", "builtins.object"], "names": {".class": "SymbolTable", "basic_config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "tests.test_advanced_crawler.TestAdvancedCrawler.basic_config", "name": "basic_config", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "tests.test_advanced_crawler.TestAdvancedCrawler.basic_config", "name": "basic_config", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "tests.test_advanced_crawler.pytest", "source_any": {".class": "AnyType", "missing_import_name": "tests.test_advanced_crawler.pytest", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "full_config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "tests.test_advanced_crawler.TestAdvancedCrawler.full_config", "name": "full_config", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "tests.test_advanced_crawler.TestAdvancedCrawler.full_config", "name": "full_config", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "tests.test_advanced_crawler.pytest", "source_any": {".class": "AnyType", "missing_import_name": "tests.test_advanced_crawler.pytest", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "test_anti_detection_delay": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "basic_config"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated"], "fullname": "tests.test_advanced_crawler.TestAdvancedCrawler.test_anti_detection_delay", "name": "test_anti_detection_delay", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "tests.test_advanced_crawler.TestAdvancedCrawler.test_anti_detection_delay", "name": "test_anti_detection_delay", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "tests.test_advanced_crawler.pytest", "source_any": {".class": "AnyType", "missing_import_name": "tests.test_advanced_crawler.pytest", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "test_basic_crawler_initialization": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "basic_config"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated"], "fullname": "tests.test_advanced_crawler.TestAdvancedCrawler.test_basic_crawler_initialization", "name": "test_basic_crawler_initialization", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "tests.test_advanced_crawler.TestAdvancedCrawler.test_basic_crawler_initialization", "name": "test_basic_crawler_initialization", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "tests.test_advanced_crawler.pytest", "source_any": {".class": "AnyType", "missing_import_name": "tests.test_advanced_crawler.pytest", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "test_captcha_detection": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "basic_config"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated"], "fullname": "tests.test_advanced_crawler.TestAdvancedCrawler.test_captcha_detection", "name": "test_captcha_detection", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "tests.test_advanced_crawler.TestAdvancedCrawler.test_captcha_detection", "name": "test_captcha_detection", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "tests.test_advanced_crawler.pytest", "source_any": {".class": "AnyType", "missing_import_name": "tests.test_advanced_crawler.pytest", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "test_crawler_stats": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "basic_config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.test_advanced_crawler.TestAdvancedCrawler.test_crawler_stats", "name": "test_crawler_stats", "type": null}}, "test_javascript_rendering_integration": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated"], "fullname": "tests.test_advanced_crawler.TestAdvancedCrawler.test_javascript_rendering_integration", "name": "test_javascript_rendering_integration", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "tests.test_advanced_crawler.TestAdvancedCrawler.test_javascript_rendering_integration", "name": "test_javascript_rendering_integration", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "tests.test_advanced_crawler.pytest", "source_any": {".class": "AnyType", "missing_import_name": "tests.test_advanced_crawler.pytest", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "test_proxy_pool_integration": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated"], "fullname": "tests.test_advanced_crawler.TestAdvancedCrawler.test_proxy_pool_integration", "name": "test_proxy_pool_integration", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "tests.test_advanced_crawler.TestAdvancedCrawler.test_proxy_pool_integration", "name": "test_proxy_pool_integration", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "tests.test_advanced_crawler.pytest", "source_any": {".class": "AnyType", "missing_import_name": "tests.test_advanced_crawler.pytest", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "test_response_caching": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "basic_config"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated"], "fullname": "tests.test_advanced_crawler.TestAdvancedCrawler.test_response_caching", "name": "test_response_caching", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "tests.test_advanced_crawler.TestAdvancedCrawler.test_response_caching", "name": "test_response_caching", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "tests.test_advanced_crawler.pytest", "source_any": {".class": "AnyType", "missing_import_name": "tests.test_advanced_crawler.pytest", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "test_session_management": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "basic_config"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated"], "fullname": "tests.test_advanced_crawler.TestAdvancedCrawler.test_session_management", "name": "test_session_management", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "tests.test_advanced_crawler.TestAdvancedCrawler.test_session_management", "name": "test_session_management", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "tests.test_advanced_crawler.pytest", "source_any": {".class": "AnyType", "missing_import_name": "tests.test_advanced_crawler.pytest", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "test_smart_retry_mechanism": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "basic_config"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated"], "fullname": "tests.test_advanced_crawler.TestAdvancedCrawler.test_smart_retry_mechanism", "name": "test_smart_retry_mechanism", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "tests.test_advanced_crawler.TestAdvancedCrawler.test_smart_retry_mechanism", "name": "test_smart_retry_mechanism", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "tests.test_advanced_crawler.pytest", "source_any": {".class": "AnyType", "missing_import_name": "tests.test_advanced_crawler.pytest", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "test_user_agent_rotation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "basic_config"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated"], "fullname": "tests.test_advanced_crawler.TestAdvancedCrawler.test_user_agent_rotation", "name": "test_user_agent_rotation", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "tests.test_advanced_crawler.TestAdvancedCrawler.test_user_agent_rotation", "name": "test_user_agent_rotation", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "tests.test_advanced_crawler.pytest", "source_any": {".class": "AnyType", "missing_import_name": "tests.test_advanced_crawler.pytest", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tests.test_advanced_crawler.TestAdvancedCrawler.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tests.test_advanced_crawler.TestAdvancedCrawler", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UserAgentConfig": {".class": "SymbolTableNode", "cross_ref": "src.data_trans.crawlers.user_agent_manager.UserAgentConfig", "kind": "Gdef", "module_hidden": true, "module_public": false}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.test_advanced_crawler.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.test_advanced_crawler.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.test_advanced_crawler.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.test_advanced_crawler.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.test_advanced_crawler.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.test_advanced_crawler.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "asyncio": {".class": "SymbolTableNode", "cross_ref": "asyncio", "kind": "Gdef", "module_hidden": true, "module_public": false}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef", "module_hidden": true, "module_public": false}, "patch": {".class": "SymbolTableNode", "cross_ref": "unittest.mock.patch", "kind": "Gdef", "module_hidden": true, "module_public": false}, "project_root": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "tests.test_advanced_crawler.project_root", "name": "project_root", "setter_type": null, "type": "pathlib.Path"}}, "pytest": {".class": "SymbolTableNode", "kind": "Gdef", "module_hidden": true, "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "tests.test_advanced_crawler.pytest", "name": "pytest", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "tests.test_advanced_crawler.pytest", "source_any": null, "type_of_any": 3}}}, "run_basic_test": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "tests.test_advanced_crawler.run_basic_test", "name": "run_basic_test", "type": null}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "tests\\test_advanced_crawler.py"}