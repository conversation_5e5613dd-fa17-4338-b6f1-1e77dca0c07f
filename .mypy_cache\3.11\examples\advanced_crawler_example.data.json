{".class": "MypyFile", "_fullname": "examples.advanced_crawler_example", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AdvancedCrawler": {".class": "SymbolTableNode", "cross_ref": "src.data_trans.crawlers.advanced_crawler.AdvancedCrawler", "kind": "Gdef", "module_hidden": true, "module_public": false}, "AdvancedCrawlerConfig": {".class": "SymbolTableNode", "cross_ref": "src.data_trans.crawlers.advanced_crawler.AdvancedCrawlerConfig", "kind": "Gdef", "module_hidden": true, "module_public": false}, "AntiDetectionConfig": {".class": "SymbolTableNode", "cross_ref": "src.data_trans.crawlers.anti_detection.AntiDetectionConfig", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CaptchaConfig": {".class": "SymbolTableNode", "cross_ref": "src.data_trans.crawlers.captcha_solver.CaptchaConfig", "kind": "Gdef", "module_hidden": true, "module_public": false}, "JSRendererConfig": {".class": "SymbolTableNode", "cross_ref": "src.data_trans.crawlers.js_renderer.JSRendererConfig", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Path": {".class": "SymbolTableNode", "cross_ref": "pathlib.Path", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ProxyPoolConfig": {".class": "SymbolTableNode", "cross_ref": "src.data_trans.crawlers.proxy_pool.ProxyPoolConfig", "kind": "Gdef", "module_hidden": true, "module_public": false}, "SessionConfig": {".class": "SymbolTableNode", "cross_ref": "src.data_trans.crawlers.session_manager.SessionConfig", "kind": "Gdef", "module_hidden": true, "module_public": false}, "UserAgentConfig": {".class": "SymbolTableNode", "cross_ref": "src.data_trans.crawlers.user_agent_manager.UserAgentConfig", "kind": "Gdef", "module_hidden": true, "module_public": false}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "examples.advanced_crawler_example.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "examples.advanced_crawler_example.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "examples.advanced_crawler_example.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "examples.advanced_crawler_example.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "examples.advanced_crawler_example.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "examples.advanced_crawler_example.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "anti_detection_example": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "examples.advanced_crawler_example.anti_detection_example", "name": "anti_detection_example", "type": null}}, "asyncio": {".class": "SymbolTableNode", "cross_ref": "asyncio", "kind": "Gdef", "module_hidden": true, "module_public": false}, "basic_advanced_crawler_example": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "examples.advanced_crawler_example.basic_advanced_crawler_example", "name": "basic_advanced_crawler_example", "type": null}}, "configuration_examples": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "examples.advanced_crawler_example.configuration_examples", "name": "configuration_examples", "type": null}}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "examples.advanced_crawler_example.logger", "name": "logger", "setter_type": null, "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef", "module_hidden": true, "module_public": false}, "main": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "examples.advanced_crawler_example.main", "name": "main", "type": null}}, "project_root": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "examples.advanced_crawler_example.project_root", "name": "project_root", "setter_type": null, "type": "pathlib.Path"}}, "proxy_pool_example": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "examples.advanced_crawler_example.proxy_pool_example", "name": "proxy_pool_example", "type": null}}, "session_management_example": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "examples.advanced_crawler_example.session_management_example", "name": "session_management_example", "type": null}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "examples\\advanced_crawler_example.py"}