"""
爬虫组件独立测试

测试各个爬虫组件的功能，避免复杂的依赖问题。
"""

import asyncio
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


def test_user_agent_manager():
    """测试User-Agent管理器"""
    print("测试User-Agent管理器...")

    try:
        from src.data_trans.crawlers.user_agent_manager import (
            UserAgentConfig,
            UserAgentManager,
        )

        config = UserAgentConfig(
            use_fake_useragent=False,  # 禁用fake-useragent避免依赖问题
            fallback_enabled=True,
        )
        manager = UserAgentManager(config)

        # 测试获取随机User-Agent
        ua1 = manager.get_random_user_agent()
        ua2 = manager.get_random_user_agent("chrome")
        ua3 = manager.get_user_agent_by_rotation()

        print(f"  ✓ 随机User-Agent: {ua1[:50]}...")
        print(f"  ✓ Chrome User-Agent: {ua2[:50]}...")
        print(f"  ✓ 轮换User-Agent: {ua3[:50]}...")

        # 测试移动端User-Agent
        mobile_ua = manager.get_mobile_user_agent("android")
        print(f"  ✓ 移动端User-Agent: {mobile_ua[:50]}...")

        # 测试统计信息
        stats = manager.stats
        print(f"  ✓ 统计信息: {stats}")

        print("✓ User-Agent管理器测试通过")
        return True

    except Exception as e:
        print(f"✗ User-Agent管理器测试失败: {e}")
        return False


def test_proxy_pool():
    """测试代理池"""
    print("测试代理池...")

    try:
        from src.data_trans.crawlers.proxy_pool import (
            ProxyInfo,
            ProxyPool,
            ProxyPoolConfig,
        )

        config = ProxyPoolConfig(
            max_proxies=5,
            enable_free_sources=False,  # 禁用免费源避免网络请求
            check_interval=60.0,
        )

        pool = ProxyPool(config)

        # 手动添加测试代理
        test_proxy = ProxyInfo(host="127.0.0.1", port=8080, protocol="http")

        pool._proxies["127.0.0.1:8080"] = test_proxy
        pool._working_proxies = ["127.0.0.1:8080"]

        # 测试代理属性
        print(f"  ✓ 代理URL: {test_proxy.url}")
        print(f"  ✓ 代理成功率: {test_proxy.success_rate}")

        # 测试统计信息
        stats = pool.stats
        print(f"  ✓ 统计信息: {stats}")

        print("✓ 代理池测试通过")
        return True

    except Exception as e:
        print(f"✗ 代理池测试失败: {e}")
        return False


def test_anti_detection():
    """测试反检测功能"""
    print("测试反检测功能...")

    try:
        from src.data_trans.crawlers.anti_detection import (
            AntiDetection,
            AntiDetectionConfig,
        )

        config = AntiDetectionConfig(
            enable_random_delay=True,
            min_delay=0.1,
            max_delay=0.5,
            enable_header_rotation=True,
            enable_user_agent_rotation=False,  # 禁用UA轮换避免依赖问题
        )

        anti_detection = AntiDetection(config)

        # 测试随机请求头
        headers = anti_detection.get_random_headers()
        print(f"  ✓ 随机请求头数量: {len(headers)}")
        print(f"  ✓ 请求头示例: {list(headers.keys())[:3]}")

        # 测试重试判断
        should_retry_403 = anti_detection.should_retry(403, "blocked")
        should_retry_200 = anti_detection.should_retry(200, "success")
        print(f"  ✓ 403状态码重试判断: {should_retry_403}")
        print(f"  ✓ 200状态码重试判断: {should_retry_200}")

        # 测试重试延迟
        delay = anti_detection.get_retry_delay(1)
        print(f"  ✓ 重试延迟: {delay:.2f}秒")

        # 测试会话轮换判断
        should_rotate = anti_detection.should_rotate_session()
        print(f"  ✓ 会话轮换判断: {should_rotate}")

        # 测试统计信息
        stats = anti_detection.stats
        print(f"  ✓ 统计信息: {stats}")

        print("✓ 反检测功能测试通过")
        return True

    except Exception as e:
        print(f"✗ 反检测功能测试失败: {e}")
        return False


def test_captcha_solver_config():
    """测试验证码识别器配置"""
    print("测试验证码识别器配置...")

    try:
        from src.data_trans.crawlers.captcha_solver import CaptchaConfig, CaptchaResult

        config = CaptchaConfig(solver_type="2captcha", api_key="test_key", timeout=10.0)

        # 测试配置
        print(f"  ✓ 验证码服务类型: {config.solver_type}")
        print(f"  ✓ 支持的验证码类型: {config.supported_types}")
        print(f"  ✓ 超时时间: {config.timeout}")

        # 测试结果模型
        result = CaptchaResult(success=True, solution="test_solution", solve_time=2.5)

        print(f"  ✓ 验证码结果: success={result.success}, solution={result.solution}")

        print("✓ 验证码识别器配置测试通过")
        return True

    except Exception as e:
        print(f"✗ 验证码识别器配置测试失败: {e}")
        return False


def test_js_renderer_config():
    """测试JavaScript渲染器配置"""
    print("测试JavaScript渲染器配置...")

    try:
        from src.data_trans.crawlers.js_renderer import JSRendererConfig, JSRenderResult

        config = JSRendererConfig(
            renderer_type="playwright",
            headless=True,
            timeout=30.0,
            browser_type="chromium",
        )

        print(f"  ✓ 渲染器类型: {config.renderer_type}")
        print(f"  ✓ 无头模式: {config.headless}")
        print(f"  ✓ 浏览器类型: {config.browser_type}")
        print(f"  ✓ 超时时间: {config.timeout}")

        # 测试结果模型
        result = JSRenderResult(
            url="http://example.com",
            html="<html><body>Test</body></html>",
            status_code=200,
            render_time=1.5,
        )

        print(f"  ✓ 渲染结果: url={result.url}, status={result.status_code}")

        print("✓ JavaScript渲染器配置测试通过")
        return True

    except Exception as e:
        print(f"✗ JavaScript渲染器配置测试失败: {e}")
        return False


def test_session_info():
    """测试会话信息模型"""
    print("测试会话信息模型...")

    try:
        import time

        from src.data_trans.crawlers.session_manager import SessionInfo

        session = SessionInfo(
            session_id="test_session",
            created_at=time.time() - 100,
            last_used=time.time() - 10,
        )

        # 测试属性
        print(f"  ✓ 会话ID: {session.session_id}")
        print(f"  ✓ 会话年龄: {session.age:.1f}秒")
        print(f"  ✓ 空闲时间: {session.idle_time:.1f}秒")
        print(f"  ✓ 成功率: {session.success_rate}")

        # 测试请求统计
        session.success_count = 8
        session.failure_count = 2
        print(f"  ✓ 更新后成功率: {session.success_rate}")

        print("✓ 会话信息模型测试通过")
        return True

    except Exception as e:
        print(f"✗ 会话信息模型测试失败: {e}")
        return False


def test_advanced_crawler_config():
    """测试高级爬虫配置"""
    print("测试高级爬虫配置...")

    try:
        from src.data_trans.crawlers.advanced_crawler import AdvancedCrawlerConfig
        from src.data_trans.crawlers.proxy_pool import ProxyPoolConfig
        from src.data_trans.crawlers.user_agent_manager import UserAgentConfig

        config = AdvancedCrawlerConfig(
            enable_proxy_pool=True,
            proxy_pool_config=ProxyPoolConfig(max_proxies=10),
            enable_user_agent_rotation=True,
            user_agent_config=UserAgentConfig(),
            enable_anti_detection=True,
            enable_session_management=True,
            enable_smart_retry=True,
            smart_retry_max_attempts=3,
        )

        print(f"  ✓ 启用代理池: {config.enable_proxy_pool}")
        print(f"  ✓ 启用UA轮换: {config.enable_user_agent_rotation}")
        print(f"  ✓ 启用反检测: {config.enable_anti_detection}")
        print(f"  ✓ 启用会话管理: {config.enable_session_management}")
        print(f"  ✓ 智能重试次数: {config.smart_retry_max_attempts}")

        print("✓ 高级爬虫配置测试通过")
        return True

    except Exception as e:
        print(f"✗ 高级爬虫配置测试失败: {e}")
        return False


def main():
    """运行所有测试"""
    print("开始爬虫组件测试...\n")

    tests = [
        test_user_agent_manager,
        test_proxy_pool,
        test_anti_detection,
        test_captcha_solver_config,
        test_js_renderer_config,
        test_session_info,
        test_advanced_crawler_config,
    ]

    passed = 0
    total = len(tests)

    for test in tests:
        try:
            result = test()
            if result:
                passed += 1
            print()
        except Exception as e:
            print(f"✗ 测试异常: {e}\n")

    print(f"测试完成: {passed}/{total} 通过")

    if passed == total:
        print("🎉 所有测试通过！")
        print("\n高级爬虫功能实现完成，包括：")
        print("- ✓ 代理池管理和轮换")
        print("- ✓ User-Agent轮换")
        print("- ✓ 反检测功能")
        print("- ✓ 验证码识别接口")
        print("- ✓ JavaScript渲染支持")
        print("- ✓ 会话管理")
        print("- ✓ 智能重试策略")
    else:
        print("⚠️  部分测试失败")


if __name__ == "__main__":
    main()
