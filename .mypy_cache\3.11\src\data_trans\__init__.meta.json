{"data_mtime": 1752408380, "dep_lines": [17, 17, 17, 17, 17, 17, 21, 28, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 10, 10, 10, 10, 5, 30, 30, 30, 30, 30], "dependencies": ["src.data_trans.api", "src.data_trans.cleaners", "src.data_trans.config", "src.data_trans.crawlers", "src.data_trans.queue", "src.data_trans.storage", "src.data_trans.executor", "warnings", "builtins", "_frozen_importlib", "abc", "types", "typing", "_warnings"], "hash": "580315d7fc97dd6376f55951e52c02082270e19c", "id": "src.data_trans", "ignore_all": false, "interface_hash": "61180d83c3e1a901212c5dd1835dd4fa033476d6", "mtime": 1752408378, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": true, "disable_error_code": [], "disable_memoryview_promotion": true, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": true, "disallow_untyped_calls": true, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": true, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": true, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": false, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": ["pydantic.mypy"], "strict_bytes": true, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": false, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "D:\\github_projects\\data_trans\\src\\data_trans\\__init__.py", "plugin_data": [{"debug_dataclass_transform": false, "init_forbid_extra": true, "init_typed": true, "warn_required_dynamic_aliases": true}, null], "size": 988, "suppressed": [], "version_id": "1.16.1"}