"""
高级爬虫使用示例

演示如何使用高级爬虫功能，包括代理池、User-Agent轮换、
JavaScript渲染、验证码识别、反检测等功能。
"""

import asyncio
import logging
import sys
from pathlib import Path

from src.data_trans.crawlers.advanced_crawler import (
    AdvancedCrawler,
    AdvancedCrawlerConfig,
)
from src.data_trans.crawlers.anti_detection import AntiDetectionConfig
from src.data_trans.crawlers.captcha_solver import CaptchaConfig
from src.data_trans.crawlers.js_renderer import JSRendererConfig
from src.data_trans.crawlers.proxy_pool import ProxyPoolConfig
from src.data_trans.crawlers.session_manager import SessionConfig
from src.data_trans.crawlers.user_agent_manager import UserAgentConfig

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 配置日志
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


async def basic_advanced_crawler_example():
    """基础高级爬虫示例"""
    print("=== 基础高级爬虫示例 ===")

    # 创建基础配置
    config = AdvancedCrawlerConfig(
        # 基础爬虫配置
        timeout=30.0,
        max_retries=3,
        # 启用基础高级功能
        enable_user_agent_rotation=True,
        enable_anti_detection=True,
        enable_session_management=True,
        enable_smart_retry=True,
        # 禁用需要外部服务的功能
        enable_proxy_pool=False,
        enable_js_rendering=False,
        enable_captcha_solving=False,
        # 配置User-Agent管理
        user_agent_config=UserAgentConfig(
            use_fake_useragent=False, fallback_enabled=True  # 避免依赖问题
        ),
        # 配置反检测
        anti_detection_config=AntiDetectionConfig(
            enable_random_delay=True,
            min_delay=1.0,
            max_delay=3.0,
            enable_header_rotation=True,
            enable_user_agent_rotation=False,  # 由UA管理器处理
        ),
        # 配置会话管理
        session_config=SessionConfig(
            max_sessions=3,
            enable_cookie_persistence=False,  # 避免文件操作
            enable_health_check=False,  # 避免网络请求
        ),
        # 智能重试配置
        smart_retry_max_attempts=3,
    )

    # 创建高级爬虫
    crawler = AdvancedCrawler(config)

    try:
        # 初始化爬虫
        await crawler.setup()
        print("✓ 高级爬虫初始化成功")

        # 测试URL列表
        test_urls = [
            "https://httpbin.org/json",
            "https://httpbin.org/headers",
            "https://httpbin.org/user-agent",
        ]

        # 爬取测试
        for url in test_urls:
            try:
                print(f"\n爬取URL: {url}")
                result = await crawler.fetch_single(url)

                if result.error:
                    print(f"  ✗ 爬取失败: {result.error}")
                else:
                    print(f"  ✓ 状态码: {result.status_code}")
                    print(f"  ✓ 耗时: {result.duration:.2f}秒")
                    print(f"  ✓ 数据大小: {len(str(result.data))} 字符")

            except Exception as e:
                print(f"  ✗ 爬取异常: {e}")

        # 显示统计信息
        stats = crawler.stats
        print(f"\n统计信息: {stats}")

    finally:
        # 清理资源
        await crawler.cleanup()
        print("\n✓ 高级爬虫资源已清理")


async def proxy_pool_example():
    """代理池示例"""
    print("\n=== 代理池示例 ===")

    # 创建带代理池的配置
    config = AdvancedCrawlerConfig(
        enable_proxy_pool=True,
        proxy_pool_config=ProxyPoolConfig(
            max_proxies=5,
            enable_free_sources=False,  # 生产环境中可以启用
            check_interval=300.0,
        ),
        enable_user_agent_rotation=True,
        user_agent_config=UserAgentConfig(use_fake_useragent=False),
        enable_anti_detection=True,
    )

    crawler = AdvancedCrawler(config)

    try:
        await crawler.setup()

        # 手动添加测试代理（实际使用中会从代理源获取）
        if crawler._proxy_pool:
            from src.data_trans.crawlers.proxy_pool import ProxyInfo

            test_proxies = [
                ProxyInfo(host="127.0.0.1", port=8080, protocol="http"),
                ProxyInfo(host="127.0.0.1", port=8081, protocol="http"),
            ]

            for proxy in test_proxies:
                proxy_key = f"{proxy.host}:{proxy.port}"
                crawler._proxy_pool._proxies[proxy_key] = proxy
                crawler._proxy_pool._working_proxies.append(proxy_key)

            print(f"✓ 添加了 {len(test_proxies)} 个测试代理")

            # 显示代理池统计
            stats = crawler._proxy_pool.stats
            print(f"代理池统计: {stats}")

    finally:
        await crawler.cleanup()


async def session_management_example():
    """会话管理示例"""
    print("\n=== 会话管理示例 ===")

    config = AdvancedCrawlerConfig(
        enable_session_management=True,
        session_config=SessionConfig(
            max_sessions=5, session_timeout=60.0, enable_cookie_persistence=False
        ),
        enable_anti_detection=True,
    )

    crawler = AdvancedCrawler(config)

    try:
        await crawler.setup()

        if crawler._session_manager:
            # 获取多个会话
            sessions = []
            for i in range(3):
                session_info = await crawler._session_manager.get_session()
                if session_info:
                    session_id, client, semaphore = session_info
                    sessions.append(session_id)
                    print(f"✓ 获取会话: {session_id}")

            # 显示会话统计
            stats = crawler._session_manager.stats
            print(f"会话管理统计: {stats}")

    finally:
        await crawler.cleanup()


async def anti_detection_example():
    """反检测功能示例"""
    print("\n=== 反检测功能示例 ===")

    config = AdvancedCrawlerConfig(
        enable_anti_detection=True,
        anti_detection_config=AntiDetectionConfig(
            enable_random_delay=True,
            min_delay=0.5,
            max_delay=2.0,
            enable_header_rotation=True,
            enable_request_pattern_randomization=True,
            enable_smart_retry=True,
        ),
    )

    crawler = AdvancedCrawler(config)

    try:
        await crawler.setup()

        if crawler._anti_detection:
            # 测试随机请求头
            headers1 = crawler._anti_detection.get_random_headers()
            headers2 = crawler._anti_detection.get_random_headers()

            print(f"✓ 请求头1数量: {len(headers1)}")
            print(f"✓ 请求头2数量: {len(headers2)}")
            print(f"✓ 请求头是否不同: {headers1 != headers2}")

            # 测试重试判断
            retry_cases = [
                (200, "success"),
                (403, "forbidden"),
                (429, "rate limited"),
                (503, "service unavailable"),
            ]

            for status_code, content in retry_cases:
                should_retry = crawler._anti_detection.should_retry(
                    status_code, content
                )
                print(f"✓ 状态码 {status_code}: 是否重试 = {should_retry}")

            # 显示统计信息
            stats = crawler._anti_detection.stats
            print(f"反检测统计: {stats}")

    finally:
        await crawler.cleanup()


async def configuration_examples():
    """配置示例"""
    print("\n=== 配置示例 ===")

    # JavaScript渲染配置
    js_config = JSRendererConfig(
        renderer_type="playwright",
        headless=True,
        timeout=30.0,
        browser_type="chromium",
        stealth_mode=True,
        block_images=True,  # 提高性能
        block_css=True,
    )
    print(
        f"✓ JavaScript渲染配置: {js_config.renderer_type}, 无头模式: {js_config.headless}"
    )

    # 验证码识别配置
    captcha_config = CaptchaConfig(
        solver_type="2captcha",
        api_key="your_api_key_here",
        timeout=120.0,
        supported_types=["image", "recaptcha_v2", "recaptcha_v3"],
    )
    print(
        f"✓ 验证码识别配置: {captcha_config.solver_type}, 支持类型: {captcha_config.supported_types}"
    )

    # 完整高级配置
    AdvancedCrawlerConfig(
        # 基础配置
        timeout=30.0,
        max_retries=3,
        # 启用所有高级功能
        enable_proxy_pool=True,
        enable_user_agent_rotation=True,
        enable_js_rendering=True,
        enable_captcha_solving=True,
        enable_anti_detection=True,
        enable_session_management=True,
        enable_smart_retry=True,
        enable_response_caching=True,
        # 详细配置
        proxy_pool_config=ProxyPoolConfig(max_proxies=20),
        user_agent_config=UserAgentConfig(),
        js_renderer_config=js_config,
        captcha_config=captcha_config,
        anti_detection_config=AntiDetectionConfig(),
        session_config=SessionConfig(max_sessions=10),
        # 性能配置
        smart_retry_max_attempts=5,
        cache_ttl=300.0,
    )

    print("✓ 完整配置创建成功，启用功能数量: 8")


async def main():
    """主函数"""
    print("高级爬虫功能演示\n")

    examples = [
        basic_advanced_crawler_example,
        proxy_pool_example,
        session_management_example,
        anti_detection_example,
        configuration_examples,
    ]

    for example in examples:
        try:
            await example()
        except Exception as e:
            print(f"✗ 示例执行失败: {e}")

        print("\n" + "=" * 50)

    print("\n🎉 高级爬虫功能演示完成！")
    print("\n主要功能包括：")
    print("- ✅ 代理池管理和轮换")
    print("- ✅ User-Agent轮换")
    print("- ✅ 反检测功能（随机延迟、请求头伪装）")
    print("- ✅ 验证码识别接口")
    print("- ✅ JavaScript渲染支持")
    print("- ✅ 会话管理和Cookie池")
    print("- ✅ 智能重试策略")
    print("- ✅ 响应缓存")


if __name__ == "__main__":
    asyncio.run(main())
