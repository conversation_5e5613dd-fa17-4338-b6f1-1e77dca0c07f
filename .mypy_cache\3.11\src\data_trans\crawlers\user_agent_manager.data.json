{".class": "MypyFile", "_fullname": "src.data_trans.crawlers.user_agent_manager", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BaseModel": {".class": "SymbolTableNode", "cross_ref": "pydantic.main.BaseModel", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Field": {".class": "SymbolTableNode", "cross_ref": "pydantic.fields.Field", "kind": "Gdef", "module_hidden": true, "module_public": false}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_hidden": true, "module_public": false}, "UserAgent": {".class": "SymbolTableNode", "kind": "Gdef", "module_hidden": true, "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "src.data_trans.crawlers.user_agent_manager.UserAgent", "name": "UserAgent", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "src.data_trans.crawlers.user_agent_manager.UserAgent", "source_any": null, "type_of_any": 3}}}, "UserAgentConfig": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pydantic.main.BaseModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "src.data_trans.crawlers.user_agent_manager.UserAgentConfig", "name": "UserAgentConfig", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "src.data_trans.crawlers.user_agent_manager.UserAgentConfig", "has_param_spec_type": false, "metaclass_type": "pydantic._internal._model_construction.ModelMetaclass", "metadata": {"pydantic-mypy-metadata": {"class_vars": {}, "config": {}, "fields": {"browser_weights": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 54, "name": "browser_weights", "strict": null, "type": {".class": "Instance", "args": ["builtins.str", "builtins.float"], "extra_attrs": null, "type_ref": "builtins.dict"}}, "browsers": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 27, "name": "browsers", "strict": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}, "cache_enabled": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 24, "name": "cache_enabled", "strict": null, "type": "builtins.bool"}, "custom_user_agents": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 37, "name": "custom_user_agents", "strict": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}, "fallback_enabled": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 23, "name": "fallback_enabled", "strict": null, "type": "builtins.bool"}, "operating_systems": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 32, "name": "operating_systems", "strict": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}, "use_fake_useragent": {"alias": null, "column": 4, "has_default": true, "has_dynamic_alias": false, "is_frozen": false, "line": 22, "name": "use_fake_useragent", "strict": null, "type": "builtins.bool"}}}}, "module_name": "src.data_trans.crawlers.user_agent_manager", "mro": ["src.data_trans.crawlers.user_agent_manager.UserAgentConfig", "pydantic.main.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["__pydantic_self__", "use_fake_useragent", "fallback_enabled", "cache_enabled", "browsers", "operating_systems", "custom_user_agents", "browser_weights"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.data_trans.crawlers.user_agent_manager.UserAgentConfig.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["__pydantic_self__", "use_fake_useragent", "fallback_enabled", "cache_enabled", "browsers", "operating_systems", "custom_user_agents", "browser_weights"], "arg_types": ["src.data_trans.crawlers.user_agent_manager.UserAgentConfig", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str", "builtins.float"], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of UserAgentConfig", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "browser_weights": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.crawlers.user_agent_manager.UserAgentConfig.browser_weights", "name": "browser_weights", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", "builtins.float"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "browsers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.crawlers.user_agent_manager.UserAgentConfig.browsers", "name": "browsers", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "cache_enabled": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.crawlers.user_agent_manager.UserAgentConfig.cache_enabled", "name": "cache_enabled", "setter_type": null, "type": "builtins.bool"}}, "custom_user_agents": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.crawlers.user_agent_manager.UserAgentConfig.custom_user_agents", "name": "custom_user_agents", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "fallback_enabled": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.crawlers.user_agent_manager.UserAgentConfig.fallback_enabled", "name": "fallback_enabled", "setter_type": null, "type": "builtins.bool"}}, "model_construct": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 5, 5, 5, 5, 5, 5, 5], "arg_names": [null, "_fields_set", "use_fake_useragent", "fallback_enabled", "cache_enabled", "browsers", "operating_systems", "custom_user_agents", "browser_weights"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "src.data_trans.crawlers.user_agent_manager.UserAgentConfig.model_construct", "name": "model_construct", "type": {".class": "CallableType", "arg_kinds": [0, 1, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["_cls", "_fields_set", "use_fake_useragent", "fallback_enabled", "cache_enabled", "browsers", "operating_systems", "custom_user_agents", "browser_weights"], "arg_types": [{".class": "TypeType", "item": "src.data_trans.crawlers.user_agent_manager.UserAgentConfig"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str", "builtins.float"], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "model_construct of UserAgentConfig", "ret_type": "src.data_trans.crawlers.user_agent_manager.UserAgentConfig", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready"], "fullname": "src.data_trans.crawlers.user_agent_manager.UserAgentConfig.model_construct", "name": "model_construct", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 1, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["_cls", "_fields_set", "use_fake_useragent", "fallback_enabled", "cache_enabled", "browsers", "operating_systems", "custom_user_agents", "browser_weights"], "arg_types": [{".class": "TypeType", "item": "src.data_trans.crawlers.user_agent_manager.UserAgentConfig"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str", "builtins.float"], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "model_construct of UserAgentConfig", "ret_type": "src.data_trans.crawlers.user_agent_manager.UserAgentConfig", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "operating_systems": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.crawlers.user_agent_manager.UserAgentConfig.operating_systems", "name": "operating_systems", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "use_fake_useragent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "src.data_trans.crawlers.user_agent_manager.UserAgentConfig.use_fake_useragent", "name": "use_fake_useragent", "setter_type": null, "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "src.data_trans.crawlers.user_agent_manager.UserAgentConfig.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "src.data_trans.crawlers.user_agent_manager.UserAgentConfig", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UserAgentManager": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "src.data_trans.crawlers.user_agent_manager.UserAgentManager", "name": "UserAgentManager", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "src.data_trans.crawlers.user_agent_manager.UserAgentManager", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "src.data_trans.crawlers.user_agent_manager", "mro": ["src.data_trans.crawlers.user_agent_manager.UserAgentManager", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "src.data_trans.crawlers.user_agent_manager.UserAgentManager.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "config"], "arg_types": ["src.data_trans.crawlers.user_agent_manager.UserAgentManager", "src.data_trans.crawlers.user_agent_manager.UserAgentConfig"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of UserAgentManager", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_build_cache": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "src.data_trans.crawlers.user_agent_manager.UserAgentManager._build_cache", "name": "_build_cache", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["src.data_trans.crawlers.user_agent_manager.UserAgentManager"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_build_cache of UserAgentManager", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_choose_browser_by_weight": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "src.data_trans.crawlers.user_agent_manager.UserAgentManager._choose_browser_by_weight", "name": "_choose_browser_by_weight", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["src.data_trans.crawlers.user_agent_manager.UserAgentManager"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_choose_browser_by_weight of UserAgentManager", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_current_indices": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "src.data_trans.crawlers.user_agent_manager.UserAgentManager._current_indices", "name": "_current_indices", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_fake_ua": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "src.data_trans.crawlers.user_agent_manager.UserAgentManager._fake_ua", "name": "_fake_ua", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "src.data_trans.crawlers.user_agent_manager.UserAgent", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_get_fallback_user_agent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "src.data_trans.crawlers.user_agent_manager.UserAgentManager._get_fallback_user_agent", "name": "_get_fallback_user_agent", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["src.data_trans.crawlers.user_agent_manager.UserAgentManager"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_fallback_user_agent of UserAgentManager", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_user_agent_for_browser": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "browser"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "src.data_trans.crawlers.user_agent_manager.UserAgentManager._get_user_agent_for_browser", "name": "_get_user_agent_for_browser", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "browser"], "arg_types": ["src.data_trans.crawlers.user_agent_manager.UserAgentManager", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_user_agent_for_browser of UserAgentManager", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_user_agent_cache": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "src.data_trans.crawlers.user_agent_manager.UserAgentManager._user_agent_cache", "name": "_user_agent_cache", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "add_custom_user_agent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "user_agent"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "src.data_trans.crawlers.user_agent_manager.UserAgentManager.add_custom_user_agent", "name": "add_custom_user_agent", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "user_agent"], "arg_types": ["src.data_trans.crawlers.user_agent_manager.UserAgentManager", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "add_custom_user_agent of UserAgentManager", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "src.data_trans.crawlers.user_agent_manager.UserAgentManager.config", "name": "config", "setter_type": null, "type": "src.data_trans.crawlers.user_agent_manager.UserAgentConfig"}}, "get_mobile_user_agent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "platform"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "src.data_trans.crawlers.user_agent_manager.UserAgentManager.get_mobile_user_agent", "name": "get_mobile_user_agent", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "platform"], "arg_types": ["src.data_trans.crawlers.user_agent_manager.UserAgentManager", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_mobile_user_agent of UserAgentManager", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_random_user_agent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "browser"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "src.data_trans.crawlers.user_agent_manager.UserAgentManager.get_random_user_agent", "name": "get_random_user_agent", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "browser"], "arg_types": ["src.data_trans.crawlers.user_agent_manager.UserAgentManager", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_random_user_agent of UserAgentManager", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_user_agent_by_rotation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "browser"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "src.data_trans.crawlers.user_agent_manager.UserAgentManager.get_user_agent_by_rotation", "name": "get_user_agent_by_rotation", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "browser"], "arg_types": ["src.data_trans.crawlers.user_agent_manager.UserAgentManager", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_user_agent_by_rotation of UserAgentManager", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_user_agents_for_browser": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "browser", "count"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "src.data_trans.crawlers.user_agent_manager.UserAgentManager.get_user_agents_for_browser", "name": "get_user_agents_for_browser", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "browser", "count"], "arg_types": ["src.data_trans.crawlers.user_agent_manager.UserAgentManager", "builtins.str", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_user_agents_for_browser of UserAgentManager", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "refresh_cache": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "src.data_trans.crawlers.user_agent_manager.UserAgentManager.refresh_cache", "name": "refresh_cache", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["src.data_trans.crawlers.user_agent_manager.UserAgentManager"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "refresh_cache of UserAgentManager", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "remove_custom_user_agent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "user_agent"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "src.data_trans.crawlers.user_agent_manager.UserAgentManager.remove_custom_user_agent", "name": "remove_custom_user_agent", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "user_agent"], "arg_types": ["src.data_trans.crawlers.user_agent_manager.UserAgentManager", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "remove_custom_user_agent of UserAgentManager", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "stats": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "src.data_trans.crawlers.user_agent_manager.UserAgentManager.stats", "name": "stats", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["src.data_trans.crawlers.user_agent_manager.UserAgentManager"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "stats of UserAgentManager", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "any"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "src.data_trans.crawlers.user_agent_manager.UserAgentManager.stats", "name": "stats", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["src.data_trans.crawlers.user_agent_manager.UserAgentManager"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "stats of UserAgentManager", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "any"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "src.data_trans.crawlers.user_agent_manager.UserAgentManager.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "src.data_trans.crawlers.user_agent_manager.UserAgentManager", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.crawlers.user_agent_manager.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.crawlers.user_agent_manager.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.crawlers.user_agent_manager.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.crawlers.user_agent_manager.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.crawlers.user_agent_manager.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.data_trans.crawlers.user_agent_manager.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "src.data_trans.crawlers.user_agent_manager.logger", "name": "logger", "setter_type": null, "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef", "module_hidden": true, "module_public": false}, "random": {".class": "SymbolTableNode", "cross_ref": "random", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "D:\\github_projects\\data_trans\\src\\data_trans\\crawlers\\user_agent_manager.py"}