{"data_mtime": 1752408380, "dep_lines": [9, 5, 6, 7, 8, 1, 1, 1, 1, 1, 1, 1, 1, 16, 22, 23, 24, 29, 30, 11], "dep_prios": [5, 10, 10, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 5, 5, 5, 5, 5, 5, 10], "dependencies": ["unittest.mock", "asyncio", "os", "sys", "datetime", "builtins", "_frozen_importlib", "abc", "typing", "unittest", "asyncio.runners", "ntpath", "posixpath"], "hash": "bec2cf39607c3cf35afb5cfc9131ca0fbb34979e", "id": "tests.test_workflow", "ignore_all": false, "interface_hash": "65c5a7120cd4e80645bc84e32c0b5256509207f9", "mtime": 1752421602, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": true, "disable_error_code": [], "disable_memoryview_promotion": true, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": true, "disallow_untyped_calls": true, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": true, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": true, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": false, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": ["pydantic.mypy"], "strict_bytes": true, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": false, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "tests\\test_workflow.py", "plugin_data": [{"debug_dataclass_transform": false, "init_forbid_extra": true, "init_typed": true, "warn_required_dynamic_aliases": true}, null], "size": 13508, "suppressed": ["data_trans.workflow.base", "data_trans.workflow.dag_templates", "data_trans.workflow.notifications", "data_trans.workflow.operators", "data_trans.workflow.scheduler", "data_trans.workflow.sensors", "pytest"], "version_id": "1.16.1"}